<template>
  <div class="operation-logs-page">
    <!-- 操作日志横幅 -->
    <div class="operation-logs-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">操作日志</h1>
          <p class="banner-subtitle">查看和管理系统操作记录，追踪文件变更历史</p>
          <div class="banner-actions">
            <el-button 
              type="primary" 
              size="large" 
              @click="exportLogs" 
              :loading="exporting"
              class="action-button primary-button"
            >
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
            <el-button 
              type="success" 
              size="large" 
              @click="handleRefresh"
              class="action-button refresh-button"
            >
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button
              v-if="authStore.isAdmin"
              type="warning"
              size="large"
              @click="cleanupTestData"
              :loading="cleaningTest"
              class="action-button warning-button"
            >
              <el-icon><Delete /></el-icon>
              清理测试数据
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="operation-logs-icon">
            <el-icon><List /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        操作统计
      </h2>
      <div class="stats-overview">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statsData.totalCount || total }}</div>
            <div class="stat-label">总操作数</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ successCount }}</div>
            <div class="stat-label">成功操作</div>
            <div class="stat-trend">
              <el-icon><Check /></el-icon>
              <span>操作成功</span>
            </div>
          </div>
        </div>
        <div class="stat-card danger">
          <div class="stat-icon">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ failedCount }}</div>
            <div class="stat-label">失败操作</div>
            <div class="stat-trend">
              <el-icon><Close /></el-icon>
              <span>需要关注</span>
            </div>
          </div>
        </div>
        <div class="stat-card info">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ todayCount }}</div>
            <div class="stat-label">今日操作</div>
            <div class="stat-trend">
              <el-icon><Calendar /></el-icon>
              <span>24小时内</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化筛选条件区域 -->
    <div class="filter-section">
      <div class="filter-header">
        <div class="filter-title-group">
          <h2 class="section-title">
            <el-icon><Filter /></el-icon>
            筛选条件
          </h2>
          <div class="filter-quick-actions">
            <el-button
              v-if="hasActiveFilters"
              @click="handleResetFilters"
              size="small"
              round
              class="reset-btn"
            >
              <el-icon><Refresh /></el-icon>
              重置筛选
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 快捷筛选标签 -->
      <div class="quick-filters">
        <div class="quick-filter-label">
          <el-icon><Lightning /></el-icon>
          快捷筛选：
        </div>
        <el-tag
          v-for="quick in quickFilters"
          :key="quick.key"
          :type="isQuickFilterActive(quick) ? 'primary' : 'info'"
          :effect="isQuickFilterActive(quick) ? 'dark' : 'plain'"
          @click.stop="applyQuickFilter(quick)"
          class="quick-filter-tag"
          :class="{ active: isQuickFilterActive(quick) }"
        >
          <el-icon class="tag-icon">
            <component :is="quick.icon" />
          </el-icon>
          {{ quick.label }}
          <span v-if="quick.count" class="tag-count">{{ quick.count }}</span>
        </el-tag>
      </div>
      
      <!-- 高级筛选卡片组 -->
      <div class="filter-cards-container">
        <!-- 操作类型卡片 -->
        <div class="filter-card modern">
          <div class="filter-card-header">
            <div class="card-icon-wrapper">
              <el-icon class="card-icon"><Operation /></el-icon>
            </div>
            <div class="card-info">
              <h4 class="card-title">操作类型</h4>
              <p class="card-subtitle">选择要查看的操作类型</p>
            </div>
          </div>
          <div class="filter-card-body">
            <el-select
              v-model="filters.action"
              placeholder="全部操作类型"
              clearable
              size="default"
              class="modern-select"
              popper-class="modern-select-dropdown"
              :teleported="true"
              @change="handleFilterChange"
            >
              <template #prefix>
                <el-icon class="select-prefix-icon"><Operation /></el-icon>
              </template>
              <el-option label="上传文件" value="upload" />
              <el-option label="下载文件" value="download" />
              <el-option label="删除文件" value="delete" />
              <el-option label="重命名文件" value="rename" />
              <el-option label="创建目录" value="create_directory" />
              <el-option label="删除目录" value="delete_directory" />
              <el-option label="重命名目录" value="rename_directory" />
            </el-select>
          </div>
        </div>
        
        <!-- 操作状态卡片 -->
        <div class="filter-card modern">
          <div class="filter-card-header">
            <div class="card-icon-wrapper status">
              <el-icon class="card-icon"><InfoFilled /></el-icon>
            </div>
            <div class="card-info">
              <h4 class="card-title">操作状态</h4>
              <p class="card-subtitle">筛选成功或失败的操作</p>
            </div>
          </div>
          <div class="filter-card-body">
            <el-select
              v-model="filters.status"
              placeholder="全部状态"
              clearable
              size="default"
              class="modern-select"
              popper-class="modern-select-dropdown"
              :teleported="true"
              @change="handleFilterChange"
            >
              <template #prefix>
                <el-icon class="select-prefix-icon"><InfoFilled /></el-icon>
              </template>
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
            </el-select>
          </div>
        </div>
        
        <!-- 时间范围卡片 -->
        <div class="filter-card modern time-filter">
          <div class="filter-card-header">
            <div class="card-icon-wrapper time">
              <el-icon class="card-icon"><Calendar /></el-icon>
            </div>
            <div class="card-info">
              <h4 class="card-title">时间范围</h4>
              <p class="card-subtitle">选择操作时间段</p>
            </div>
          </div>
          <div class="filter-card-body">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="default"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
              class="modern-date-picker"
              :prefix-icon="Calendar"
              :clearable="true"
              :editable="false"
              :teleported="true"
            />
          </div>
        </div>
      </div>
      
      <!-- 搜索操作栏 -->
      <div class="filter-actions-bar">
        <div class="active-filters-summary" v-if="hasActiveFilters">
          <span class="summary-label">当前筛选：</span>
          <el-tag
            v-if="filters.action"
            closable
            @close="filters.action = ''"
            size="small"
            class="filter-tag"
          >
            操作类型：{{ getActionText(filters.action) }}
          </el-tag>
          <el-tag
            v-if="filters.status"
            closable
            @close="filters.status = ''"
            size="small"
            class="filter-tag"
          >
            状态：{{ filters.status === 'success' ? '成功' : '失败' }}
          </el-tag>
          <el-tag
            v-if="(dateRange && dateRange.length === 2) || (filters.start_date && filters.end_date)"
            closable
            @close="clearDateFilter"
            size="small"
            class="filter-tag"
          >
            时间：{{ getDateRangeText() }}
          </el-tag>
        </div>
        <div class="action-buttons">
          <el-button
            type="primary"
            size="default"
            @click="applyFilters"
            :loading="loading"
            class="filter-action-btn primary-filter-btn"
          >
            <el-icon><Search /></el-icon>
            应用筛选
          </el-button>
          <el-button
            type="default"
            size="default"
            @click="handleResetFilters"
            class="filter-action-btn reset-filter-btn"
          >
            <el-icon><Refresh /></el-icon>
            重置筛选
          </el-button>
        </div>
      </div>
    </div>

    <!-- 日志列表卡片 -->
    <div class="logs-card">
      <div class="card-header">
        <div class="card-title">
          <el-icon><List /></el-icon>
          操作记录
        </div>
        <div class="card-actions">
          <!-- 批量操作按钮 -->
          <div class="batch-actions" v-if="selectedLogs.length > 0">
            <el-tag type="info" size="small" class="selection-count">
              已选择 {{ selectedLogs.length }} 项
            </el-tag>
            <el-button 
              type="danger" 
              size="small" 
              @click="showBatchDeleteDialog"
              :loading="batchDeleting"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
            <el-button 
              text 
              size="small" 
              @click="clearSelection"
            >
              取消选择
            </el-button>
          </div>
          
          <!-- 常规操作按钮 -->
          <div class="regular-actions">
            <el-button 
              text 
              size="small" 
              @click="toggleSelectAll"
              v-if="logs.length > 0"
            >
              <el-icon><Select /></el-icon>
              {{ isAllSelected ? '取消全选' : '全选' }}
            </el-button>
            <el-button text @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="card-content">
        <!-- 空状态 -->
        <div v-if="!loading && logs.length === 0" class="empty-state">
          <div class="empty-illustration">
            <div class="empty-icon">
              <el-icon><DocumentRemove /></el-icon>
            </div>
            <div class="empty-content">
              <h3>暂无操作记录</h3>
              <p>当前筛选条件下没有找到操作记录</p>
              <el-button type="primary" @click="handleResetFilters">
                重置筛选条件
              </el-button>
            </div>
          </div>
        </div>

        <!-- 日志列表 - 时间线卡片布局 -->
        <div v-else class="logs-list">
          <div v-loading="loading" class="timeline-container">
            <div class="timeline-list">
              <div
                v-for="(log, index) in logs"
                :key="log.id"
                class="timeline-item"
                :class="{ 'timeline-item-failed': !log.success }"
              >
                <!-- 时间线连接线 -->
                <div class="timeline-line" v-if="index < logs.length - 1"></div>
                
                <!-- 时间线节点 -->
                <div class="timeline-node" :class="getTimelineNodeClass(log)">
                  <el-icon>
                    <Upload v-if="log.operation_type === 'upload'" />
                    <Download v-else-if="log.operation_type === 'download'" />
                    <Delete v-else-if="log.operation_type === 'delete'" />
                    <Edit v-else-if="log.operation_type === 'rename'" />
                    <FolderAdd v-else-if="log.operation_type === 'create_directory'" />
                    <FolderDelete v-else-if="log.operation_type === 'delete_directory'" />
                    <FolderOpened v-else-if="log.operation_type === 'rename_directory'" />
                    <Operation v-else />
                  </el-icon>
                </div>
                
                <!-- 日志内容卡片 -->
                <div class="timeline-content">
                  <div class="log-card" @click="toggleLogDetail(log.id)">
                    <!-- 选择复选框 -->
                    <div class="log-checkbox" @click.stop>
                      <el-checkbox 
                        :model-value="selectedLogs.includes(log.id)"
                        @change="(checked: any) => toggleLogSelection(log.id, checked)"
                        size="small"
                      />
                    </div>
                    <div class="log-header">
                      <div class="log-main-info">
                        <div class="log-action">
                          <span class="action-text">{{ getActionText(log.operation_type) }}</span>
                          <el-tag
                            :type="log.success ? 'success' : 'danger'"
                            size="small"
                            class="status-tag"
                          >
                            {{ log.success ? '成功' : '失败' }}
                          </el-tag>
                        </div>
                        <div class="log-resource">
                          <el-icon class="resource-icon"><Document /></el-icon>
                          <span class="resource-path">{{ getDisplayFileName(log) }}</span>
                        </div>
                      </div>
                      <div class="log-meta">
                        <div class="log-time">
                          <el-icon><Clock /></el-icon>
                          <span>{{ formatDate(log.created_at) }}</span>
                        </div>
                        <div class="log-actions">
                          <el-button
                            size="small"
                            text
                            @click.stop="toggleLogDetail(log.id)"
                            class="detail-btn"
                          >
                            <el-icon><View /></el-icon>
                          </el-button>
                          <el-button
                            size="small"
                            type="danger"
                            text
                            @click.stop="deleteLog(log)"
                            :loading="deletingIds.includes(log.id)"
                            class="delete-btn"
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 变更详情 -->
                    <div v-if="log.old_name && log.new_name" class="log-changes">
                      <div class="change-item">
                        <span class="change-label">原名称:</span>
                        <span class="old-name">{{ log.old_name }}</span>
                      </div>
                      <div class="change-arrow">
                        <el-icon><Right /></el-icon>
                      </div>
                      <div class="change-item">
                        <span class="change-label">新名称:</span>
                        <span class="new-name">{{ log.new_name }}</span>
                      </div>
                    </div>
                    
                    <!-- 展开的详细信息 -->
                    <div v-if="expandedLogs.includes(log.id)" class="log-details">
                      <div class="detail-row">
                        <span class="detail-label">操作ID:</span>
                        <span class="detail-value">{{ log.id }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">资源信息:</span>
                        <span class="detail-value code">{{ log.file_path || '未知' }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">操作时间:</span>
                        <span class="detail-value">{{ formatDate(log.created_at) }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">相对时间:</span>
                        <span class="detail-value">{{ getRelativeTime(log.created_at) }}</span>
                      </div>
                      <div v-if="!log.success" class="detail-row error">
                        <span class="detail-label">错误信息:</span>
                        <span class="detail-value">{{ log.error_message || '未知错误' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="smartPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
              background
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 批量删除确认对话框 -->
    <el-dialog
      v-model="batchDeleteDialogVisible"
      title="批量删除操作日志"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="batch-delete-content">
        <div class="warning-section">
          <el-icon class="warning-icon" color="#E6A23C"><InfoFilled /></el-icon>
          <div class="warning-text">
            <h4>确认删除操作</h4>
            <p>您即将删除 <strong>{{ selectedLogs.length }}</strong> 条操作日志记录。</p>
            <p class="warning-note">此操作不可撤销，请谨慎操作！</p>
          </div>
        </div>
        
        <div class="delete-options" v-if="authStore.isAdmin">
          <el-divider>删除选项</el-divider>
          <el-radio-group v-model="batchDeleteType" size="small">
            <el-radio value="selected">删除选中的日志</el-radio>
            <el-radio value="conditions">按条件删除</el-radio>
          </el-radio-group>
          
          <div v-if="batchDeleteType === 'conditions'" class="condition-filters">
            <el-form :model="batchDeleteConditions" label-width="80px" size="small">
              <el-form-item label="操作类型">
                <el-select v-model="batchDeleteConditions.action" placeholder="选择操作类型" clearable>
                  <el-option label="文件上传" value="upload" />
                  <el-option label="文件下载" value="download" />
                  <el-option label="文件删除" value="delete" />
                  <el-option label="文件重命名" value="rename" />
                  <el-option label="创建目录" value="create_directory" />
                  <el-option label="删除目录" value="delete_directory" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="batchDeleteConditions.status" placeholder="选择状态" clearable>
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="error" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="batchDeleteConditions.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="small"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <div class="confirmation-section">
          <el-checkbox v-model="deleteConfirmation" size="small">
            我确认要执行此删除操作
          </el-checkbox>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDeleteDialogVisible = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="confirmBatchDelete"
            :loading="batchDeleting"
            :disabled="!deleteConfirmation"
          >
            确认删除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, markRaw, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentCopy,
  Download,
  DataAnalysis,
  CircleCheck,
  CircleClose,
  Clock,
  Filter,
  Refresh,
  Upload,
  Delete,
  Edit,
  Search,
  List,
  Document,
  Right,
  Operation,
  DocumentRemove,
  TrendCharts,
  Check,
  Close,
  Calendar,
  View,
  FolderAdd,
  FolderDelete,
  FolderOpened,
  Lightning,
  InfoFilled,
  Select
} from '@element-plus/icons-vue'
import { apiMethods } from '@/utils/api'
import type { OperationLog } from '@/types'
import { useAuthStore } from '@/stores/auth'

// 后端返回的日志数据结构
interface BackendLog {
  id: number
  user_id: number
  action: string
  resource: string
  old_value: string
  new_value: string
  status: string
  error_msg: string
  created_at: string
  user?: {
    id: number
    username: string
    email: string
  }
}

// 转换后端数据到前端格式
const transformLogData = (backendLog: BackendLog): OperationLog => {
  return {
    id: backendLog.id,
    user_id: backendLog.user_id,
    operation_type: backendLog.action,
    file_path: backendLog.resource,
    old_name: backendLog.old_value,
    new_name: backendLog.new_value,
    success: backendLog.status === 'success',
    error_message: backendLog.error_msg,
    created_at: backendLog.created_at
  }
}

const logs = ref<OperationLog[]>([])
const loading = ref(false)
const exporting = ref(false)
const cleaningTest = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dateRange = ref<[string, string] | undefined>()
const deletingIds = ref<number[]>([])
const expandedLogs = ref<number[]>([])

// 批量删除相关状态
const selectedLogs = ref<number[]>([])
const batchDeleting = ref(false)
const batchDeleteDialogVisible = ref(false)
const deleteConfirmation = ref(false)
const batchDeleteType = ref<'selected' | 'conditions'>('selected')
const batchDeleteConditions = ref({
  action: '',
  status: '',
  dateRange: undefined as [string, string] | undefined
})

// 使用真实的认证状态
const authStore = useAuthStore()

const filters = ref({
  action: '',
  status: '',
  start_date: '',
  end_date: ''
})

// 计算统计数据 - 基于全部数据而非当前页
const statsData = ref({
  totalCount: 0,
  successCount: 0,
  failedCount: 0,
  todayCount: 0
})

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await apiMethods.logs.getStats()
    const stats = response.data.data
    
    // 计算总数、成功数、失败数
    let totalCount = 0
    let successCount = 0
    let failedCount = 0
    
    if (stats.status_stats) {
      stats.status_stats.forEach((item: any) => {
        totalCount += item.count
        if (item.status === 'success') {
          successCount = item.count
        } else if (item.status === 'failed') {
          failedCount = item.count
        }
      })
    }
    
    // 计算今日操作数
    let todayCount = 0
    if (stats.daily_stats && stats.daily_stats.length > 0) {
      const today = new Date().toISOString().split('T')[0]
      const todayStats = stats.daily_stats.find((item: any) => item.date === today)
      if (todayStats) {
        todayCount = todayStats.count
      }
    }
    
    statsData.value = {
      totalCount,
      successCount,
      failedCount,
      todayCount
    }
    
    // 更新快捷筛选计数
    updateQuickFilterCounts()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 使用计算属性返回统计值
const successCount = computed(() => statsData.value.successCount)
const failedCount = computed(() => statsData.value.failedCount)
const todayCount = computed(() => statsData.value.todayCount)

// 快捷筛选配置 - 简化版本
const quickFilters = shallowRef([
  {
    key: 'today',
    label: '今日操作',
    icon: markRaw(Calendar),
    count: 0,
    action: () => {
      const today = new Date()
      const todayStr = today.toISOString().split('T')[0]
      console.log('Today filter applied:', { start_date: todayStr, end_date: todayStr })

      filters.value.start_date = todayStr
      filters.value.end_date = todayStr
    }
  },
  {
    key: 'success',
    label: '成功操作',
    icon: markRaw(CircleCheck),
    count: 0,
    action: () => {
      console.log('Success filter applied')
      filters.value.status = 'success'
    }
  },
  {
    key: 'failed',
    label: '失败操作',
    icon: markRaw(CircleClose),
    count: 0,
    action: () => {
      console.log('Failed filter applied')
      filters.value.status = 'failed'
    }
  },
  {
    key: 'upload',
    label: '上传操作',
    icon: markRaw(Upload),
    count: 0,
    action: () => {
      console.log('Upload filter applied')
      filters.value.action = 'upload'
    }
  }
])

// 更新快捷筛选的计数
const updateQuickFilterCounts = () => {
  if (quickFilters.value.length >= 4) {
    quickFilters.value[0].count = todayCount.value  // 今日操作
    quickFilters.value[1].count = successCount.value // 成功操作
    quickFilters.value[2].count = failedCount.value  // 失败操作
    // 上传操作的计数可以从统计数据中获取，暂时不显示
    quickFilters.value[3].count = 0 // 上传操作计数，可以后续从统计数据获取
  }
}

// 检查是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return !!(filters.value.action || filters.value.status ||
    filters.value.start_date || filters.value.end_date)
})

// 缓存今日日期，避免重复计算
const todayDate = new Date().toISOString().split('T')[0]

// 简化的快捷筛选激活状态检查
const isQuickFilterActive = (quick: any) => {
  // 检查是否只有该筛选条件被设置，其他条件都为空
  switch (quick.key) {
    case 'today':
      return filters.value.start_date === todayDate &&
             filters.value.end_date === todayDate &&
             !filters.value.action &&
             !filters.value.status
    case 'success':
      return filters.value.status === 'success' &&
             !filters.value.action &&
             !filters.value.start_date &&
             !filters.value.end_date
    case 'failed':
      return filters.value.status === 'failed' &&
             !filters.value.action &&
             !filters.value.start_date &&
             !filters.value.end_date
    case 'upload':
      return filters.value.action === 'upload' &&
             !filters.value.status &&
             !filters.value.start_date &&
             !filters.value.end_date
    default:
      return false
  }
}

// 简化的快捷筛选应用逻辑
const applyQuickFilter = async (quick: any) => {
  console.log('applyQuickFilter called with:', quick.key, quick.label)

  // 防止重复点击
  if (loading.value) {
    console.log('Loading in progress, skipping:', quick.key)
    return
  }

  try {
    // 检查当前筛选是否已经激活
    const isCurrentlyActive = isQuickFilterActive(quick)
    
    if (isCurrentlyActive) {
      // 如果已激活，则重置所有筛选
      console.log('Quick filter already active, resetting all filters')
      await resetAllFilters()
    } else {
      // 如果未激活，则应用该筛选
      console.log('Applying quick filter:', quick.key)
      await applySpecificQuickFilter(quick)
    }
  } catch (error) {
    console.error('Quick filter application failed:', error)
    ElMessage.error('筛选操作失败')
  }
}

// 重置所有筛选条件
const resetAllFilters = async () => {
  console.log('Resetting all filters...')
  
  // 重置筛选条件
  filters.value = {
    action: '',
    status: '',
    start_date: '',
    end_date: ''
  }
  dateRange.value = undefined
  currentPage.value = 1
  
  console.log('Filters reset, reloading data...')
  
  // 重新加载数据
  await loadLogs(true)
  await loadStats()
}

// 应用特定的快捷筛选
const applySpecificQuickFilter = async (quick: any) => {
  console.log('Applying specific quick filter:', quick.key)
  
  // 先完全重置所有筛选条件，确保干净状态
  filters.value = {
    action: '',
    status: '',
    start_date: '',
    end_date: ''
  }
  dateRange.value = undefined
  currentPage.value = 1
  
  console.log('Filters reset, applying new filter...')
  
  // 等待一个微任务周期，确保响应式更新完成
  await nextTick()
  
  // 执行快捷筛选的特定逻辑
  quick.action()
  
  console.log('Filter action executed, current filters:', filters.value)
  
  // 再等待一个微任务周期，确保新的筛选条件设置完成
  await nextTick()
  
  // 应用筛选
  await loadLogs(true)
  await loadStats()
}

// 智能分页大小计算 - 基于数据量和性能的动态算法
const smartPageSizes = computed(() => {
  const totalCount = total.value
  const baseSizes = [10, 20, 50]
  
  // 动态计算合适的分页选项
  const calculateOptimalSizes = (count: number) => {
    const sizes = [...baseSizes]
    
    // 根据数据量动态添加选项
    if (count > 50) sizes.push(100)
    if (count > 200) sizes.push(200)
    if (count > 500) sizes.push(500)
    if (count > 1000) sizes.push(1000)
    
    // 对于大数据量，添加智能分段
    if (count > 2000) {
      const segments = Math.min(5, Math.floor(count / 1000)) // 最多5个分段
      for (let i = 2; i <= segments; i++) {
        sizes.push(i * 1000)
      }
    }
    
    // 添加四分之一、二分之一选项（仅当数据量较大时）
    if (count > 1000) {
      const quarter = Math.ceil(count / 4)
      const half = Math.ceil(count / 2)
      
      if (quarter > sizes[sizes.length - 1]) sizes.push(quarter)
      if (half > sizes[sizes.length - 1]) sizes.push(half)
    }
    
    // 添加全部选项（仅当数据量不超过10000时，避免性能问题）
    if (count <= 10000 && count > sizes[sizes.length - 1]) {
      sizes.push(count)
    }
    
    return sizes.sort((a, b) => a - b) // 确保排序
  }
  
  return calculateOptimalSizes(totalCount)
})

const loadLogs = async (resetPage = false) => {
  try {
    loading.value = true
    
    // 如果需要重置页码（比如筛选条件改变时）
    if (resetPage) {
      currentPage.value = 1
    }
    
    // 构建请求参数，过滤掉空值
    const params: any = {
      page: currentPage.value,
      limit: pageSize.value
    }
    
    // 只添加非空的筛选条件
    if (filters.value.action) {
      params.action = filters.value.action
    }
    if (filters.value.status) {
      params.status = filters.value.status
    }
    if (filters.value.start_date) {
      params.start_date = filters.value.start_date
    }
    if (filters.value.end_date) {
      params.end_date = filters.value.end_date
    }
    
    console.log('Loading logs with params:', params)
    console.log('Current filters:', filters.value)
    console.log('Current dateRange:', dateRange.value)
    
    const response = await apiMethods.logs.list(params)
    
    // 添加详细的调试信息
    console.log('API Response:', response.data)
    console.log('Response data structure:', {
      success: response.data.success,
      data: response.data.data,
      logs: response.data.data?.logs,
      pagination: response.data.data?.pagination
    })
    
    // 检查响应结构
    if (!response.data.success) {
      throw new Error(response.data.message || 'API请求失败')
    }
    
    if (!response.data.data || !response.data.data.logs) {
      console.warn('API响应中没有logs数据')
      logs.value = []
      total.value = 0
      return
    }
    
    // 转换后端数据格式
    const backendLogs = response.data.data.logs as BackendLog[]
    console.log('Backend logs count:', backendLogs.length)
    console.log('First log sample:', backendLogs[0])
    
    logs.value = backendLogs.map(transformLogData)
    total.value = response.data.data.pagination.total
    
    console.log('Transformed logs count:', logs.value.length)
    console.log('Total count:', total.value)
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

// 简化的筛选条件变化处理
const handleFilterChange = async () => {
  console.log('Filter changed, applying filters...')
  await applyFilters()
}

const handleDateChange = async (dates: [string, string] | null) => {
  if (dates && dates.length === 2) {
    // 确保日期格式正确 (YYYY-MM-DD)
    const startDate = new Date(dates[0]).toISOString().split('T')[0]
    const endDate = new Date(dates[1]).toISOString().split('T')[0]

    filters.value.start_date = startDate
    filters.value.end_date = endDate
    dateRange.value = [startDate, endDate]

    console.log('Date filter applied:', { start_date: startDate, end_date: endDate })
  } else {
    filters.value.start_date = ''
    filters.value.end_date = ''
    dateRange.value = undefined

    console.log('Date filter cleared')
  }

  // 自动应用筛选
  await applyFilters()
}

// 简化的重置筛选函数
const handleResetFilters = async () => {
  console.log('handleResetFilters called')
  
  if (loading.value) {
    console.log('Loading in progress, skipping reset')
    return
  }

  try {
    await resetAllFilters()
    ElMessage.success('筛选条件已重置')
  } catch (error) {
    console.error('Reset filters failed:', error)
    ElMessage.error('重置筛选失败')
  }
}

// 获取日期范围文本
const getDateRangeText = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`
  } else if (filters.value.start_date && filters.value.end_date) {
    if (filters.value.start_date === filters.value.end_date) {
      return filters.value.start_date
    } else {
      return `${filters.value.start_date} 至 ${filters.value.end_date}`
    }
  }
  return ''
}

// 使用普通对象避免markRaw的类型问题，这些是静态数据不需要响应式
const actionClassMap: Record<string, string> = {
  upload: 'action-upload',
  download: 'action-download',
  delete: 'action-delete',
  rename: 'action-rename',
  create_directory: 'action-create-directory',
  delete_directory: 'action-delete-directory',
  rename_directory: 'action-rename-directory'
}

const actionTextMap: Record<string, string> = {
  upload: '上传',
  download: '下载',
  delete: '删除',
  rename: '重命名',
  create_directory: '创建目录',
  delete_directory: '删除目录',
  rename_directory: '重命名目录'
}

const getActionClass = (action: string) => {
  return actionClassMap[action] || 'action-default'
}

const getActionText = (action: string) => {
  return actionTextMap[action] || action
}

const deleteLog = async (log: OperationLog) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条日志记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    deletingIds.value.push(log.id)
    
    // 添加详细的调试信息
    console.log('Deleting log with ID:', log.id)
    console.log('Current user auth state:', authStore.token, authStore.user?.id)
    
    const response = await apiMethods.logs.delete(log.id)
    console.log('Delete response:', response.data)
    
    if (response.data.success) {
      ElMessage.success('日志删除成功')
      // 重新加载数据
      loadLogs()
      loadStats()
    } else {
      throw new Error(response.data.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除日志失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '删除日志失败'
      ElMessage.error('删除日志失败: ' + errorMessage)
    }
  } finally {
    deletingIds.value = deletingIds.value.filter(id => id !== log.id)
  }
}

const exportLogs = async () => {
  try {
    exporting.value = true
    const exportData = {
      action: filters.value.action,
      status: filters.value.status,
      start_date: filters.value.start_date,
      end_date: filters.value.end_date,
      format: 'csv'
    }
    
    const response = await apiMethods.logs.export(exportData)
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `operation_logs_${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('日志导出成功')
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  } finally {
    exporting.value = false
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getRelativeTime = (date: string) => {
  const now = new Date()
  const logDate = new Date(date)
  const diff = now.getTime() - logDate.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return '一周前'
}

// 获取显示的文件名
const getDisplayFileName = (log: OperationLog) => {
  // 根据操作类型优化显示逻辑
  switch (log.operation_type) {
    case 'upload':
      // 上传操作：优先显示新值（原始文件名），其次显示资源名
      return log.new_name || log.file_path || '上传文件'
    
    case 'download':
      // 下载操作：显示资源名（现在应该是原始文件名）
      return log.file_path || '下载文件'
    
    case 'delete':
      // 删除操作：优先显示旧值（原始文件名），其次显示资源名
      return log.old_name || log.file_path || '已删除文件'
    
    case 'rename':
      // 重命名操作：显示新名称
      return log.new_name || log.file_path || '重命名文件'
    
    case 'create_directory':
      // 创建目录：显示新值（目录名）
      return log.new_name || log.file_path || '新建目录'
    
    case 'delete_directory':
      // 删除目录：显示旧值（目录名）
      return log.old_name || log.file_path || '已删除目录'
    
    case 'rename_directory':
      // 重命名目录：显示新名称
      return log.new_name || log.file_path || '重命名目录'
    
    default:
      // 默认情况：尝试显示最有意义的信息
      if (log.new_name) return log.new_name
      if (log.old_name) return log.old_name
      if (log.file_path) {
        // 如果是服务器存储路径，尝试美化显示
        if (log.file_path.includes('uploads/') && log.file_path.includes('_')) {
          const ext = log.file_path.split('.').pop()
          return ext ? `文件.${ext}` : '文件'
        }
        return log.file_path
      }
      return '未知资源'
  }
}

const formatCompactDate = (date: string) => {
  const logDate = new Date(date)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDay = new Date(logDate.getFullYear(), logDate.getMonth(), logDate.getDate())
  
  if (logDay.getTime() === today.getTime()) {
    // 今天，只显示时间
    return logDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    // 其他日期，显示月日
    return logDate.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

const getTimelineNodeClass = (log: OperationLog) => {
  const baseClass = 'timeline-node'
  const actionClass = getActionClass(log.operation_type)
  const statusClass = log.success ? 'success' : 'failed'
  return `${baseClass} ${actionClass} ${statusClass}`
}

const toggleLogDetail = (logId: number) => {
  const index = expandedLogs.value.indexOf(logId)
  if (index > -1) {
    expandedLogs.value.splice(index, 1)
  } else {
    expandedLogs.value.push(logId)
  }
}

// 清除日期筛选
const clearDateFilter = async () => {
  filters.value.start_date = ''
  filters.value.end_date = ''
  dateRange.value = undefined
  await applyFilters()
}

// 简化的应用筛选条件函数
const applyFilters = async () => {
  console.log('Executing applyFilters...')
  
  if (loading.value) {
    console.log('Already loading, skipping applyFilters...')
    return
  }

  try {
    await loadLogs(true) // 重置页码并加载
    await loadStats()
    console.log('Filter application completed')
  } catch (error) {
    console.error('Error during filter application:', error)
    ElMessage.error('应用筛选失败')
  }
}



// 刷新数据
const handleRefresh = async () => {
  await loadLogs()
  await loadStats()
}

// 监听分页变化
const handlePageChange = async () => {
  await loadLogs()
}

// 监听每页数量变化
const handleSizeChange = async () => {
  currentPage.value = 1 // 重置到第一页
  await loadLogs()
}

// 批量删除相关方法

// 计算属性：是否全选
const isAllSelected = computed(() => {
  return logs.value.length > 0 && selectedLogs.value.length === logs.value.length
})

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedLogs.value = []
  } else {
    selectedLogs.value = logs.value.map(log => log.id)
  }
}

// 清除选择
const clearSelection = () => {
  selectedLogs.value = []
}

// 切换单个日志的选择状态
const toggleLogSelection = (logId: number, checked: any) => {
  if (checked) {
    if (!selectedLogs.value.includes(logId)) {
      selectedLogs.value.push(logId)
    }
  } else {
    const index = selectedLogs.value.indexOf(logId)
    if (index > -1) {
      selectedLogs.value.splice(index, 1)
    }
  }
}

// 显示批量删除对话框
const showBatchDeleteDialog = () => {
  if (selectedLogs.value.length === 0) {
    ElMessage.warning('请先选择要删除的日志')
    return
  }
  batchDeleteDialogVisible.value = true
  deleteConfirmation.value = false
  batchDeleteType.value = 'selected'
  // 重置条件筛选
  batchDeleteConditions.value = {
    action: '',
    status: '',
    dateRange: undefined
  }
}

// 确认批量删除
const confirmBatchDelete = async () => {
  if (!deleteConfirmation.value) {
    ElMessage.warning('请先确认删除操作')
    return
  }

  try {
    batchDeleting.value = true
    
    // 构建请求数据
    const requestData: any = {
      confirm: true
    }

    if (batchDeleteType.value === 'selected') {
      // 按ID删除
      requestData.delete_type = 'ids'
      requestData.log_ids = selectedLogs.value
    } else {
      // 按条件删除
      requestData.delete_type = 'conditions'
      if (batchDeleteConditions.value.action) {
        requestData.action = batchDeleteConditions.value.action
      }
      if (batchDeleteConditions.value.status) {
        requestData.status = batchDeleteConditions.value.status
      }
      if (batchDeleteConditions.value.dateRange && batchDeleteConditions.value.dateRange.length === 2) {
        requestData.start_date = batchDeleteConditions.value.dateRange[0]
        requestData.end_date = batchDeleteConditions.value.dateRange[1]
      }
    }

    // 调用批量删除API
    const response = await apiMethods.logs.batchDelete(requestData)
    
    if (response.data.success) {
      const deletedCount = response.data.data.deleted_count
      ElMessage.success(`成功删除 ${deletedCount} 条日志记录`)
      
      // 关闭对话框并重置状态
      batchDeleteDialogVisible.value = false
      selectedLogs.value = []
      deleteConfirmation.value = false
      
      // 重新加载数据
      loadLogs()
      loadStats()
    } else {
      ElMessage.error(response.data.message || '批量删除失败')
    }
  } catch (error: any) {
    console.error('批量删除失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '批量删除失败'
    ElMessage.error(errorMessage)
  } finally {
    batchDeleting.value = false
  }
}

// 清理测试数据
const cleanupTestData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有测试相关的日志数据吗？此操作不可撤销！',
      '清理测试数据',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    cleaningTest.value = true
    
    console.log('Cleaning up test data...')
    const response = await apiMethods.logs.cleanupTest()
    console.log('Cleanup response:', response.data)
    
    if (response.data.success) {
      const deletedCount = response.data.data.deleted_count
      ElMessage.success(`成功清理 ${deletedCount} 条测试日志记录`)
      
      // 重新加载数据
      loadLogs()
      loadStats()
    } else {
      throw new Error(response.data.message || '清理失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('清理测试数据失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '清理测试数据失败'
      ElMessage.error('清理失败: ' + errorMessage)
    }
  } finally {
    cleaningTest.value = false
  }
}

onMounted(() => {
  loadLogs()
  loadStats()
})
</script>

<style scoped>
.operation-logs-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 操作日志横幅 - 更加紧凑 */
.operation-logs-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 12px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 美化操作按钮样式 */
.action-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 12px 24px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
}

.primary-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
}

.primary-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.refresh-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

.refresh-button:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3) !important;
}

.warning-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
}

.warning-button:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3) !important;
}

/* 筛选操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-end; /* 右对齐按钮 */
  margin-left: auto; /* 确保按钮始终在右边 */
}

.filter-action-btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
  border: 1px solid #d1d5db !important;
}

.primary-filter-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
  border: none !important;
}

.primary-filter-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.25) !important;
}

.reset-filter-btn {
  background: white !important;
  color: #6b7280 !important;
  border: 1px solid #d1d5db !important;
}

.reset-filter-btn:hover {
  background: #f9fafb !important;
  color: #374151 !important;
  border-color: #9ca3af !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.operation-logs-banner .banner-title,
.operation-logs-page .banner-title,
h1.banner-title,
.operation-logs-banner h1,
.operation-logs-page h1,
.banner-title {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0 0 6px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.875rem;
  margin: 0 0 12px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.banner-visual {
  position: relative;
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation-logs-icon {
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.operation-logs-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .operation-logs-banner:hover .operation-logs-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 统计概览 - 更加紧凑 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 8px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: 42px;
  border-left: 2px solid transparent;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.danger {
  border-left-color: #ef4444;
}

.stat-card.info {
  border-left-color: #06b6d4;
}

.stat-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stat-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stat-card.danger:hover {
  border-left-color: #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.stat-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  position: relative;
  z-index: 1;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card.primary .stat-icon {
  background: #3b82f6;
}

.stat-card.success .stat-icon {
  background: #10b981;
}

.stat-card.danger .stat-icon {
  background: #ef4444;
}

.stat-card.info .stat-icon {
  background: #06b6d4;
}

.stat-card:hover .stat-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon .el-icon {
  font-size: 0.75rem !important;
  line-height: 1 !important;
  width: 0.75rem !important;
  height: 0.75rem !important;
  display: inline-block !important;
}

.stat-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stat-label {
  color: #6b7280;
  font-size: 0.7rem;
  font-weight: 500;
  margin-bottom: 0;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.6rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stat-trend .el-icon {
  font-size: 0.6rem;
}

/* 统计区块标题 */
.stats-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1rem;
}

/* 现代化筛选区域 - 更紧凑 */
.filter-section {
  margin-bottom: 20px;
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.filter-quick-actions {
  display: flex;
  gap: 12px;
}

.reset-btn {
  font-weight: 500;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 快捷筛选标签 - 更紧凑，修复对齐问题 */
.quick-filters {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  flex-wrap: wrap;
  min-height: 40px;
}

.quick-filter-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-right: 8px;
  line-height: 1.2;
  height: 24px;
}

.quick-filter-label .el-icon {
  color: #3b82f6;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.quick-filter-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 0.8125rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  user-select: none;
  height: 24px;
  line-height: 1.2;
}

.quick-filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.quick-filter-tag.active {
  transform: scale(1.02);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  background: var(--el-color-primary) !important;
  color: white !important;
  border-color: var(--el-color-primary) !important;
}

.quick-filter-tag.active .tag-icon {
  color: white !important;
}

.quick-filter-tag.active .tag-count {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.tag-icon {
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.tag-count {
  background: rgba(255, 255, 255, 0.3);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 4px;
  transition: all 0.3s ease;
}

/* 筛选卡片容器 - 更紧凑的布局 */
.filter-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

/* 现代化筛选卡片 - 缩小尺寸 */
.filter-card.modern {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  min-height: 80px;
}

.filter-card.modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter-card.modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.filter-card.modern:hover::before {
  opacity: 1;
}

.filter-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #f1f5f9;
}

.card-icon-wrapper {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.15);
  flex-shrink: 0;
}

.card-icon-wrapper.status {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.2);
}

.card-icon-wrapper.time {
  background: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 100%);
  box-shadow: 0 2px 6px rgba(6, 182, 212, 0.2);
}

.card-icon {
  font-size: 0.875rem;
  color: white;
}

.card-info {
  flex: 1;
}

.card-title {
  margin: 0 0 1px 0;
  font-size: 0.8125rem;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.card-subtitle {
  margin: 0;
  font-size: 0.6875rem;
  color: #64748b;
  font-weight: 400;
  line-height: 1.2;
}

.filter-card-body {
  padding: 8px 12px;
}

/* 现代化选择器样式 */
.modern-select {
  width: 100%;
}

.modern-select :deep(.el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.3s ease;
  padding: 1px 6px;
  height: 32px;
}

.modern-select :deep(.el-input__wrapper:hover) {
  border-color: #cbd5e1;
  background: white;
}

.modern-select :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.modern-select :deep(.el-input__inner) {
  color: #1e293b !important;
  font-size: 0.875rem !important;
  font-weight: 500;
}

.modern-select :deep(.el-input__suffix) {
  color: #64748b;
}

.select-prefix-icon {
  color: #64748b;
  margin-right: 8px;
}

/* 现代化下拉选项 - 修复显示问题 */
.modern-select-dropdown {
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  overflow: hidden;
  background: white !important;
  z-index: 2000 !important;
}

.modern-select-dropdown :deep(.el-scrollbar__view) {
  padding: 4px !important;
}

.modern-select-dropdown :deep(.el-select-dropdown__list) {
  padding: 0 !important;
}

.modern-select-dropdown .el-select-dropdown__item {
  padding: 10px 16px !important;
  transition: all 0.2s ease;
  min-height: 36px !important;
  display: flex !important;
  align-items: center !important;
  font-size: 0.875rem !important;
  color: #1e293b !important;
}

.modern-select-dropdown .el-select-dropdown__item:hover {
  background: #f8fafc !important;
  color: #3b82f6 !important;
}

.modern-select-dropdown .el-select-dropdown__item.selected {
  background: rgba(59, 130, 246, 0.08) !important;
  color: #3b82f6 !important;
  font-weight: 600;
}

.modern-select-dropdown .el-select-dropdown__item.is-disabled {
  color: #94a3b8 !important;
  cursor: not-allowed;
}

/* 现代化日期选择器 */
.modern-date-picker {
  width: 100%;
}

.modern-date-picker :deep(.el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.3s ease;
  padding: 1px 6px;
  height: 32px;
}

.modern-date-picker :deep(.el-input__wrapper:hover) {
  border-color: #cbd5e1;
  background: white;
}

.modern-date-picker :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.modern-date-picker :deep(.el-range-separator) {
  color: #64748b;
  font-weight: 500;
}

/* 搜索操作栏 */
.filter-actions-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  min-height: 44px;
}

.active-filters-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  flex-wrap: wrap;
}

.summary-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-right: 4px;
}

.filter-tag {
  border-radius: 20px;
  font-size: 0.8125rem;
  padding: 4px 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  background: white;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.search-btn {
  padding: 0 20px !important;
  height: 32px !important;
  border-radius: 16px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.15) !important;
  transition: all 0.3s ease !important;
  font-size: 0.875rem !important;
}

.search-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2) !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-cards-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .filter-section {
    margin-bottom: 24px;
  }
  
  .filter-title-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .quick-filters {
    padding: 12px 16px;
    gap: 8px;
  }
  
  .quick-filter-label {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .quick-filter-tag {
    font-size: 0.8125rem;
    padding: 4px 12px;
  }
  
  .filter-cards-container {
    gap: 16px;
  }
  
  .filter-card-header {
    padding: 10px 14px;
  }
  
  .card-icon-wrapper {
    width: 40px;
    height: 40px;
  }
  
  .card-title {
    font-size: 0.9375rem;
  }
  
  .card-subtitle {
    font-size: 0.8125rem;
  }
  
  .filter-card-body {
    padding: 10px 14px;
  }
  
  .filter-actions-bar {
    flex-direction: column;
    align-items: stretch;
    padding: 16px 20px;
  }
  
  .active-filters-summary {
    margin-bottom: 12px;
  }
  
  .action-buttons {
    width: 100%;
  }
  
  .search-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .quick-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quick-filter-tag {
    justify-content: center;
  }
  
  .filter-card-header {
    padding: 8px 12px;
    gap: 8px;
  }
  
  .card-icon-wrapper {
    width: 36px;
    height: 36px;
  }
  
  .card-icon {
    font-size: 1rem;
  }
  
  .card-title {
    font-size: 0.875rem;
  }
  
  .card-subtitle {
    font-size: 0.75rem;
  }
  
  .filter-card-body {
    padding: 8px 12px;
  }
  
  .option-icon-wrapper {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
  
  .option-label {
    font-size: 0.8125rem;
  }
  
  .option-desc {
    font-size: 0.7rem;
  }
}

/* 日志卡片 - 作为主要内容 */
.logs-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 600px;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title .el-icon {
  color: #3b82f6;
}

.card-content {
  min-height: 400px;
}

/* 空状态 */
.empty-state {
  padding: 80px 24px;
  text-align: center;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #64748b;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #64748b;
}

/* 时间线布局样式 - 增加高度 */
.timeline-container {
  padding: 24px;
  min-height: 500px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 自定义滚动条样式 */
.timeline-container::-webkit-scrollbar {
  width: 8px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.timeline-list {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-line {
  position: absolute;
  left: 16px;
  top: 32px;
  bottom: -16px;
  width: 2px;
  background: linear-gradient(to bottom, #e2e8f0 0%, #f1f5f9 100%);
  z-index: 1;
}

.timeline-node {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  border: 2px solid white;
}

.timeline-node.action-upload.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.timeline-node.action-download.success {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.timeline-node.action-delete.success {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.timeline-node.action-rename.success {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.timeline-node.action-create-directory.success {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.timeline-node.action-delete-directory.success {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.timeline-node.action-rename-directory.success {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

.timeline-node.failed {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  opacity: 0.7;
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.log-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 14px 18px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.log-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.timeline-item-failed .log-card {
  border-left: 3px solid #ef4444;
}

.timeline-item-failed .log-card:hover {
  border-color: #ef4444;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  margin-bottom: 0;
}

.log-main-info {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-action {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.status-tag {
  font-weight: 500;
  border: none;
  font-size: 0.75rem;
  padding: 2px 6px;
}

.log-resource {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
}

.resource-icon {
  color: #94a3b8;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.resource-path {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
  color: #475569;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-time {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.7rem;
  color: #94a3b8;
}

.log-actions {
  display: flex;
  gap: 2px;
}

.detail-btn,
.delete-btn {
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.detail-btn:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.delete-btn:hover {
  background: #fef2f2;
  color: #ef4444;
}

.log-changes {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 0.8rem;
}

.change-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.change-label {
  font-size: 0.7rem;
  color: #94a3b8;
  font-weight: 500;
}

.old-name {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  word-break: break-all;
  font-size: 0.75rem;
}

.new-name {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  word-break: break-all;
  font-size: 0.75rem;
}

.change-arrow {
  color: #94a3b8;
  flex-shrink: 0;
  margin-top: 12px;
  font-size: 0.8rem;
}

.log-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 12px;
  margin-top: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 6px;
  font-size: 0.8rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row.error {
  background: #fef2f2;
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #ef4444;
}

.detail-label {
  font-weight: 500;
  color: #64748b;
  flex-shrink: 0;
  min-width: 70px;
  font-size: 0.75rem;
}

.detail-value {
  color: #1e293b;
  word-break: break-all;
  text-align: right;
  font-size: 0.75rem;
}

.detail-value.code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
}

.detail-row.error .detail-value {
  color: #dc2626;
}

.logs-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafbfc;
}

.logs-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: #f1f5f9;
}

/* 表格单元格样式 */
.action-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
}

/* 超紧凑样式 */
.action-cell-compact {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon-mini {
  width: 22px;
  height: 22px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.resource-cell-compact {
  display: flex;
  align-items: center;
}

.resource-name-compact {
  color: #1e293b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.7rem;
  word-break: break-all;
}

.change-detail-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
}

.old-value-compact {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.65rem;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.new-value-compact {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.65rem;
  font-weight: 500;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.change-arrow-compact {
  color: #94a3b8;
  font-size: 0.6rem;
  flex-shrink: 0;
}

.no-change-compact {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.7rem;
}

.status-icon-compact {
  font-size: 0.9rem;
}

.status-success {
  color: #10b981;
}

.status-failed {
  color: #ef4444;
}

.time-cell-compact {
  display: flex;
  justify-content: center;
}

.time-compact {
  color: #1e293b;
  font-size: 0.7rem;
  font-weight: 500;
}

.delete-btn-compact {
  color: #ef4444;
  transition: all 0.2s ease;
  padding: 2px;
  border-radius: 4px;
  font-size: 0.7rem;
}

.delete-btn-compact:hover {
  background: #fef2f2;
  color: #dc2626;
}

.action-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 2px;
}

.action-upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-download {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.action-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.action-rename {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.action-create-directory {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.action-delete-directory {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.action-rename-directory {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

.action-default {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.action-text {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.65rem;
  text-align: center;
}

.resource-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #94a3b8;
  flex-shrink: 0;
}

.resource-name {
  color: #1e293b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
}

.change-detail {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.change-label {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
  flex-shrink: 0;
}

.old-value {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
}

.new-value {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  font-weight: 500;
  word-break: break-all;
}

.change-arrow {
  color: #94a3b8;
  flex-shrink: 0;
  margin: 0 4px;
}

.no-change {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.875rem;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: center;
}

.time-date {
  color: #1e293b;
  font-size: 0.8rem;
  font-weight: 500;
}

.time-relative {
  color: #94a3b8;
  font-size: 0.7rem;
}

.delete-btn {
  color: #ef4444;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 4px;
}

.delete-btn:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* 分页 */
.pagination-container {
  padding: 24px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-logs-page {
    padding: 16px;
  }
  
  .operation-logs-banner {
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 12px;
  }
  
  .banner-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .banner-title {
    font-size: 1.125rem !important;
  }
  
  .banner-subtitle {
    font-size: 0.875rem;
  }
  
  .banner-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-card {
    padding: 10px;
    gap: 8px;
    min-height: 55px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stat-icon .el-icon {
    font-size: 0.9rem !important;
    width: 0.9rem !important;
    height: 0.9rem !important;
  }

  .stat-value {
    font-size: 1.3rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }
  
  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .table-container {
    padding: 0 16px;
  }
  
  .logs-table :deep(.el-table__body-wrapper) {
    overflow-x: auto;
  }
  
  .action-cell {
    gap: 4px;
  }
  
  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .action-text {
    font-size: 0.7rem;
  }
  
  .resource-name {
    font-size: 0.8rem;
  }
  
  .change-detail {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }
  
  .change-item {
    justify-content: flex-start;
  }
  
  .old-value,
  .new-value {
    font-size: 0.75rem;
  }
  
  .time-date {
    font-size: 0.8rem;
  }
  
  .time-relative {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.25rem !important;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stat-card {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }

  .stat-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stat-icon .el-icon {
    font-size: 0.8rem !important;
    width: 0.8rem !important;
    height: 0.8rem !important;
  }

  .stat-value {
    font-size: 1.1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
  
  .table-container {
    padding: 0 12px;
  }
  
  .logs-table :deep(.el-table th) {
    padding: 12px 8px;
    font-size: 0.8rem;
  }
  
  .logs-table :deep(.el-table td) {
    padding: 10px 8px;
  }
  
  .action-icon {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }
  
  .action-text {
    font-size: 0.65rem;
  }
  
  .resource-name {
    font-size: 0.75rem;
  }
  
  .change-detail {
    font-size: 0.75rem;
  }
  
  .old-value,
  .new-value {
    font-size: 0.7rem;
  }
  
  .time-date {
    font-size: 0.75rem;
  }
  
  .time-relative {
    font-size: 0.65rem;
  }
  
  .status-tag {
    padding: 2px 6px;
    font-size: 0.7rem;
  }
}
</style>