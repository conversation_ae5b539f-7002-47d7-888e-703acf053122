<template>
  <div class="batch-rename-page">
    <!-- 批量重命名横幅 -->
    <div class="batch-rename-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">批量重命名工具</h1>
          <p class="banner-subtitle">智能批量重命名工具，支持多种重命名规则和实时预览</p>
          <div class="banner-actions">
            <el-button type="primary" size="large" @click="$router.push('/file-manager')">
              <el-icon><FolderOpened /></el-icon>
              前往文件管理中心
            </el-button>
            <el-button size="large" @click="previewChanges" :disabled="selectedFiles.length === 0">
              <el-icon><View /></el-icon>
              预览效果
            </el-button>
            <el-button size="large" @click="downloadSelectedAsZip" :disabled="selectedFiles.length === 0">
              <el-icon><Download /></el-icon>
              ZIP下载
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="batch-rename-icon">
            <el-icon><EditPen /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title" style="margin-bottom: 40px !important;">
        <el-icon><DataAnalysis /></el-icon>
        重命名统计
      </h2>
      <div class="stats-grid">
        <div class="stats-card primary">
          <div class="stats-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ totalFiles }}</div>
            <div class="stats-label">总文件数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>可重命名文件</span>
            </div>
          </div>
        </div>

        <div class="stats-card success">
          <div class="stats-icon">
            <el-icon><Select /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ selectedFiles.length }}</div>
            <div class="stats-label">已选择</div>
            <div class="stats-trend">
              <el-icon><Check /></el-icon>
              <span>准备重命名</span>
            </div>
          </div>
        </div>

        <div class="stats-card warning">
          <div class="stats-icon">
            <el-icon><Star /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ previewCount }}</div>
            <div class="stats-label">预览效果</div>
            <div class="stats-trend">
              <el-icon><View /></el-icon>
              <span>实时预览</span>
            </div>
          </div>
        </div>

        <div class="stats-card info">
          <div class="stats-icon">
            <el-icon><Operation /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ templates.length }}</div>
            <div class="stats-label">保存模板</div>
            <div class="stats-trend">
              <el-icon><Collection /></el-icon>
              <span>快速应用</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要工作区域 -->
    <div class="main-workspace">
      <!-- 左侧：文件选择区域 -->
      <div class="workspace-left">
        <div class="section-card file-selection-card">
          <div class="section-header">
            <div class="header-left">
              <h3 class="section-title">
                <el-icon><Files /></el-icon>
                文件选择
              </h3>
              <el-tag type="info" size="small" effect="plain">
                总计: {{ files.length }} 个文件
              </el-tag>
            </div>
            <div class="section-actions">
              <el-button-group>
                <el-button size="small" @click="selectAllFiles" :disabled="files.length === 0">
                  <el-icon><Select /></el-icon>
                  全选
                </el-button>
                <el-button size="small" @click="clearSelection" :disabled="selectedFiles.length === 0">
                  <el-icon><Close /></el-icon>
                  清空
                </el-button>
              </el-button-group>
              <el-button size="small" @click="loadFiles" :loading="loading" circle>
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="section-content">
            <!-- 增强的搜索和过滤栏 -->
            <div class="enhanced-toolbar" v-if="files.length > 0">
              <div class="search-box">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索文件名..."
                  clearable
                  @input="handleSearchInput"
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="filter-controls">
                <el-select v-model="filterType" placeholder="文件类型" size="small" clearable style="width: 120px">
                  <el-option label="全部类型" value="" />
                  <el-option label="图片" value="image">
                    <span class="option-with-icon">
                      <el-icon><Picture /></el-icon>
                      图片文件
                    </span>
                  </el-option>
                  <el-option label="文档" value="document">
                    <span class="option-with-icon">
                      <el-icon><Document /></el-icon>
                      文档文件
                    </span>
                  </el-option>
                  <el-option label="视频" value="video">
                    <span class="option-with-icon">
                      <el-icon><VideoPlay /></el-icon>
                      视频文件
                    </span>
                  </el-option>
                </el-select>
                <el-dropdown @command="handleBatchCopy" :disabled="files.length === 0">
                  <el-button size="small">
                    <el-icon><CopyDocument /></el-icon>
                    批量复制
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="all-with-ext">
                        <el-icon><Document /></el-icon>
                        复制所有文件名（含后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="all-without-ext">
                        <el-icon><Edit /></el-icon>
                        复制所有文件名（无后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="selected-with-ext" :disabled="selectedFiles.length === 0">
                        <el-icon><Select /></el-icon>
                        复制选中文件名（含后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="selected-without-ext" :disabled="selectedFiles.length === 0">
                        <el-icon><Star /></el-icon>
                        复制选中文件名（无后缀）
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div v-if="filteredFiles.length === 0 && !loading" class="empty-state">
              <div class="empty-illustration">
                <div class="empty-icon">
                  <el-icon><DocumentRemove /></el-icon>
                </div>
                <div class="empty-content">
                  <h4>暂无文件</h4>
                  <p>请先在文件管理中心上传文件</p>
                  <el-button type="primary" size="small" @click="$router.push('/file-manager')">
                    前往文件管理中心
                  </el-button>
                </div>
              </div>
            </div>

            <div v-else class="file-list-container">
              <!-- 增强的表格样式 -->
              <div class="enhanced-file-table">
                <div class="table-header">
                  <div class="header-cell checkbox-cell">
                    <el-checkbox
                      :model-value="selectedFiles.length === paginatedFiles.length && paginatedFiles.length > 0"
                      :indeterminate="selectedFiles.length > 0 && selectedFiles.length < paginatedFiles.length"
                      @change="handleSelectAll"
                    />
                  </div>
                  <div class="header-cell name-cell">文件名</div>
                  <div class="header-cell size-cell">大小</div>
                  <div class="header-cell index-cell">序号</div>
                  <div class="header-cell action-cell">操作</div>
                </div>
                
                <div class="table-body">
                  <div
                    v-for="(file, index) in paginatedFiles"
                    :key="file.id"
                    class="table-row"
                    :class="{ 'selected': selectedFiles.includes(file) }"
                    @click="toggleFileSelection(file)"
                  >
                    <div class="table-cell checkbox-cell">
                      <el-checkbox
                        :model-value="selectedFiles.includes(file)"
                        @change="toggleFileSelection(file)"
                        @click.stop
                      />
                    </div>
                    <div class="table-cell name-cell">
                      <div class="file-info-enhanced">
                        <div class="file-icon-box">
                          <el-icon :style="{ color: getFileIconColor(getFileExtension(file.original_name)) }">
                            <component :is="getFileIcon(getFileExtension(file.original_name))" />
                          </el-icon>
                          <span class="file-ext-badge">{{ getFileExtension(file.original_name).toUpperCase() }}</span>
                        </div>
                        <div class="file-details">
                          <span class="file-name-text" :title="file.original_name">{{ file.original_name }}</span>
                          <span class="file-meta-info">
                            {{ formatFileSize(file.size) }} · {{ getRelativeTime(file.created_at) }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="table-cell size-cell">
                      <span class="size-text">{{ formatFileSize(file.size) }}</span>
                    </div>
                    <div class="table-cell index-cell">
                      <span class="file-index">#{{ (currentPage - 1) * pageSize + index + 1 }}</span>
                    </div>
                    <div class="table-cell action-cell">
                      <el-dropdown trigger="click" @click.stop>
                        <el-button text size="small" class="action-btn">
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item @click="copyFileName(file.original_name)">
                              <el-icon><CopyDocument /></el-icon>
                              复制完整名称
                            </el-dropdown-item>
                            <el-dropdown-item @click="copyFileNameWithoutExtension(file.original_name)">
                              <el-icon><Document /></el-icon>
                              复制无后缀名称
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 分页控件 -->
              <div class="pagination-wrapper" v-if="filteredFiles.length > pageSize">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="smartPageSizes"
                  :total="filteredFiles.length"
                  layout="total, sizes, prev, pager, next, jumper"
                  background
                  small
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：重命名规则配置 -->
      <div class="workspace-right">
        <div class="section-card rename-rules-card">
          <div class="section-header">
            <div class="header-left">
              <h3 class="section-title">
                <el-icon><EditPen /></el-icon>
                重命名规则
              </h3>
              <el-tag type="success" size="small" effect="plain" v-if="selectedFiles.length > 0">
                {{ selectedFiles.length }} 个文件待处理
              </el-tag>
            </div>
            <div class="section-actions">
              <el-dropdown @command="handleTemplateCommand">
                <el-button size="small">
                  <el-icon><Star /></el-icon>
                  智能模板
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="date-prefix">
                      <el-icon><Calendar /></el-icon>
                      日期前缀 (YYYY-MM-DD_)
                    </el-dropdown-item>
                    <el-dropdown-item command="timestamp">
                      <el-icon><Clock /></el-icon>
                      时间戳后缀 (_HHMMSS)
                    </el-dropdown-item>
                    <el-dropdown-item command="sequence">
                      <el-icon><DataAnalysis /></el-icon>
                      序号重命名 (001, 002...)
                    </el-dropdown-item>
                    <el-dropdown-item command="lowercase">
                      <el-icon><Bottom /></el-icon>
                      转换为小写
                    </el-dropdown-item>
                    <el-dropdown-item command="uppercase">
                      <el-icon><Top /></el-icon>
                      转换为大写
                    </el-dropdown-item>
                    <el-dropdown-item divided command="import">
                      <el-icon><Upload /></el-icon>
                      导入自定义规则
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="section-content">
            <div class="rule-form-modern">
              <!-- 快速操作按钮组 -->
              <div class="quick-rename-actions">
                <el-button-group>
                  <el-button
                    :type="renameType === 'prefix' ? 'primary' : ''"
                    @click="renameType = 'prefix'; debouncedInputUpdate()"
                    size="small"
                  >
                    前缀
                  </el-button>
                  <el-button
                    :type="renameType === 'suffix' ? 'primary' : ''"
                    @click="renameType = 'suffix'; debouncedInputUpdate()"
                    size="small"
                  >
                    后缀
                  </el-button>
                  <el-button
                    :type="renameType === 'replace' ? 'primary' : ''"
                    @click="renameType = 'replace'; debouncedInputUpdate()"
                    size="small"
                  >
                    替换
                  </el-button>
                  <el-button
                    :type="renameType === 'sequence' ? 'primary' : ''"
                    @click="renameType = 'sequence'; debouncedInputUpdate()"
                    size="small"
                  >
                    序号
                  </el-button>
                  <el-button
                    :type="renameType === 'regex' ? 'primary' : ''"
                    @click="renameType = 'regex'; debouncedInputUpdate()"
                    size="small"
                  >
                    正则
                  </el-button>
                </el-button-group>
              </div>

              <div class="form-group-modern">
                <label class="form-label-modern">
                  <el-icon><Setting /></el-icon>
                  重命名类型
                </label>
                <el-select
                  v-model="renameType"
                  @change="debouncedInputUpdate"
                  class="modern-select"
                  size="large"
                  placeholder="选择重命名方式"
                >
                  <el-option label="🔤 正则表达式 - 使用正则匹配和替换" value="regex" />
                  <el-option label="📝 前缀添加 - 在文件名前添加内容" value="prefix" />
                  <el-option label="📄 后缀添加 - 在文件名后添加内容" value="suffix" />
                  <el-option label="🔄 替换内容 - 查找并替换指定文本" value="replace" />
                  <el-option label="🗂️ 映射替换 - 一对一数字映射替换" value="mapping" />
                  <el-option label="🔢 序号重命名 - 按序号批量重命名" value="sequence" />
                  <el-option label="🔧 扩展名替换 - 保留文件名但替换扩展名" value="extension" />
                  <el-option label="🔠 大小写转换 - 转换文件名大小写" value="case" />
                </el-select>
              </div>

              <div v-if="renameType === 'regex'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Search /></el-icon>
                    匹配模式
                  </label>
                  <el-input
                    v-model="regexPattern"
                    placeholder="例如: photo(\d+)"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">使用正则表达式匹配文件名模式</div>
                  <div class="regex-examples">
                    <div class="example-title">常用模式：</div>
                    <div class="example-items">
                      <el-button size="small" text @click="regexPattern = 'photo(\\d+)'; debouncedInputUpdate()">
                        photo(\d+) - 匹配photo+数字
                      </el-button>
                      <el-button size="small" text @click="regexPattern = '(.+)_old'; debouncedInputUpdate()">
                        (.+)_old - 匹配以_old结尾
                      </el-button>
                      <el-button size="small" text @click="regexPattern = '^IMG_(.+)'; debouncedInputUpdate()">
                        ^IMG_(.+) - 匹配IMG_开头
                      </el-button>
                    </div>
                  </div>
                </div>
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Edit /></el-icon>
                    替换内容
                  </label>
                  <el-input
                    v-model="regexReplacement"
                    placeholder="例如: image_$1"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Edit /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">使用 $1, $2 等引用捕获组</div>
                  <div class="regex-examples">
                    <div class="example-title">替换示例：</div>
                    <div class="example-items">
                      <el-button size="small" text @click="regexReplacement = 'image_$1'; debouncedInputUpdate()">
                        image_$1 - 替换为image_+数字
                      </el-button>
                      <el-button size="small" text @click="regexReplacement = '$1_new'; debouncedInputUpdate()">
                        $1_new - 添加_new后缀
                      </el-button>
                      <el-button size="small" text @click="regexReplacement = 'renamed_$1'; debouncedInputUpdate()">
                        renamed_$1 - 添加前缀
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else-if="renameType === 'prefix'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Plus /></el-icon>
                    前缀内容
                  </label>
                  <el-input
                    v-model="prefixText"
                    placeholder="例如: 新文件_"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Plus /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">将在每个文件名前添加此内容</div>
                </div>
              </div>

              <div v-else-if="renameType === 'suffix'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Plus /></el-icon>
                    后缀内容
                  </label>
                  <el-input
                    v-model="suffixText"
                    placeholder="例如: _备份"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Plus /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">将在每个文件名后添加此内容</div>
                </div>
              </div>

              <div v-else-if="renameType === 'replace'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Search /></el-icon>
                    查找内容
                  </label>
                  <el-input
                    v-model="findText"
                    placeholder="要替换的文本"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">输入要查找的文本内容</div>
                </div>
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Edit /></el-icon>
                    替换为
                  </label>
                  <el-input
                    v-model="replaceText"
                    placeholder="新的文本"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Edit /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">输入替换后的文本内容</div>
                </div>
              </div>

              <div v-else-if="renameType === 'mapping'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Document /></el-icon>
                    原始数字列表
                  </label>
                  <el-input
                    v-model="mappingFrom"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入原始数字列表，每行一个或用空格分隔&#10;例如：&#10;9200190392641700162307&#10;9200190392641700162291&#10;9200190392641700162284"
                    @input="debouncedMappingUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Document /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">输入要被替换的原始数字，每行一个或用空格分隔</div>
                </div>
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Edit /></el-icon>
                    新数字列表
                  </label>
                  <el-input
                    v-model="mappingTo"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入新数字列表，每行一个或用空格分隔&#10;例如：&#10;BA796022416314566&#10;BA796452825314550&#10;JG810462654321445"
                    @input="debouncedMappingUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Edit /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">输入替换后的新数字，数量和顺序需与原始列表对应</div>
                </div>
                <div class="mapping-status" v-if="mappingFrom || mappingTo">
                  <div class="status-item">
                    <el-icon><InfoFilled /></el-icon>
                    <span>原始数字: {{ parseMappingList(mappingFrom).length }} 个</span>
                  </div>
                  <div class="status-item">
                    <el-icon><InfoFilled /></el-icon>
                    <span>新数字: {{ parseMappingList(mappingTo).length }} 个</span>
                  </div>
                  <div class="status-item" :class="{ 'status-warning': parseMappingList(mappingFrom).length !== parseMappingList(mappingTo).length }">
                    <el-icon><Warning v-if="parseMappingList(mappingFrom).length !== parseMappingList(mappingTo).length" /><Check v-else /></el-icon>
                    <span v-if="parseMappingList(mappingFrom).length === parseMappingList(mappingTo).length">数量匹配</span>
                    <span v-else>数量不匹配，请检查</span>
                  </div>
                </div>
              </div>

              <div v-else-if="renameType === 'sequence'" class="rule-config-modern">
                <div class="form-row-modern">
                  <div class="form-group-modern">
                    <label class="form-label-modern">
                      <el-icon><Document /></el-icon>
                      基础名称
                    </label>
                    <el-input
                      v-model="baseName"
                      placeholder="例如: 文件"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="modern-input"
                    >
                      <template #prefix>
                        <el-icon><Document /></el-icon>
                      </template>
                    </el-input>
                  </div>
                  <div class="form-group-modern">
                    <label class="form-label-modern">
                      <el-icon><Star /></el-icon>
                      起始数字
                    </label>
                    <el-input-number
                      v-model="startNumber"
                      :min="0"
                      @change="debouncedInputUpdate"
                      size="large"
                      class="modern-number-input"
                      controls-position="right"
                    />
                  </div>
                </div>
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Setting /></el-icon>
                    数字位数
                  </label>
                  <el-input-number
                    v-model="numberPadding"
                    :min="1"
                    :max="10"
                    @change="debouncedInputUpdate"
                    size="large"
                    class="modern-number-input"
                    controls-position="right"
                  />
                  <div class="input-hint">设置序号的位数，如 001, 002...</div>
                </div>
              </div>

              <div v-else-if="renameType === 'extension'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><Edit /></el-icon>
                    新扩展名
                  </label>
                  <el-input
                    v-model="newExtension"
                    placeholder="例如: txt, pdf, jpg"
                    @input="debouncedInputUpdate"
                    size="large"
                    class="modern-input"
                  >
                    <template #prefix>
                      <el-icon><Edit /></el-icon>
                    </template>
                  </el-input>
                  <div class="input-hint">输入新的文件扩展名（不需要包含点号）</div>
                </div>
              </div>

              <div v-else-if="renameType === 'case'" class="rule-config-modern">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    <el-icon><EditPen /></el-icon>
                    大小写类型
                  </label>
                  <el-select
                    v-model="caseType"
                    @change="debouncedInputUpdate"
                    class="modern-select"
                    size="large"
                    placeholder="选择大小写转换方式"
                  >
                    <el-option label="🔤 全部大写 (UPPERCASE)" value="upper" />
                    <el-option label="🔡 全部小写 (lowercase)" value="lower" />
                    <el-option label="🔠 首字母大写 (Title Case)" value="title" />
                  </el-select>
                  <div class="input-hint">选择要转换的大小写格式</div>
                </div>
              </div>

              <div class="form-actions">
                <div class="action-buttons-container">
                  <el-button
                    type="primary"
                    @click="downloadRenamedFiles"
                    :disabled="selectedFiles.length === 0 || isProcessing"
                    :loading="renaming"
                    size="large"
                    class="elegant-action-btn primary-action"
                  >
                    <el-icon><Download /></el-icon>
                    <span>下载重命名文件</span>
                  </el-button>
                  
                  <el-button
                    @click="previewChanges"
                    :disabled="selectedFiles.length === 0 || isProcessing"
                    size="large"
                    class="elegant-action-btn secondary-action"
                  >
                    <el-icon><View /></el-icon>
                    <span>预览效果</span>
                  </el-button>
                </div>
              </div>

              <!-- 进度条显示 -->
              <div v-if="showProgress" class="progress-section">
                <div class="progress-header">
                  <h4>批量重命名进度</h4>
                  <span class="progress-stats">{{ processedCount }} / {{ totalCount }}</span>
                </div>
                <el-progress
                  :percentage="processingProgress"
                  :status="errorCount > 0 ? 'warning' : 'success'"
                  :stroke-width="8"
                  striped
                  striped-flow
                />
                <div class="progress-details">
                  <span class="success-count">成功: {{ processedCount - errorCount }}</span>
                  <span v-if="errorCount > 0" class="error-count">失败: {{ errorCount }}</span>
                  <span class="remaining-count">剩余: {{ totalCount - processedCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览结果 -->
    <div v-if="previewResults.length > 0" class="preview-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><View /></el-icon>
          预览效果
        </h2>
        <el-button @click="previewResults = []" text>
          <el-icon><Close /></el-icon>
          关闭预览
        </el-button>
      </div>

      <div class="preview-content">
        <div class="preview-list">
          <div
            v-for="(result, index) in previewResults"
            :key="index"
            class="preview-item"
          >
            <div class="preview-original">
              <el-icon><Document /></el-icon>
              <span v-html="highlightDifferences(result.original, result.new, 'original')"></span>
            </div>
            <div class="preview-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
            <div class="preview-new" :class="{ 'has-changes': result.original !== result.new }">
              <el-icon><Edit /></el-icon>
              <span v-html="highlightDifferences(result.original, result.new, 'new')"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出重命名后的文件"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="export-dialog-content">
        <div class="export-info">
          <el-icon class="export-icon"><Download /></el-icon>
          <div class="export-text">
            <h3>准备导出 {{ selectedFiles.length }} 个文件</h3>
            <p>重命名完成后，您可以选择导出方式：</p>
          </div>
        </div>
        <div class="export-options">
          <el-radio-group v-model="exportType">
            <el-radio label="zip">打包为ZIP文件下载</el-radio>
            <el-radio label="individual">逐个下载文件</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="exportFiles" :loading="exporting">
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入规则对话框 -->
    <el-dialog
      v-model="showBatchImportDialog"
      title="批量导入重命名规则"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="batch-import-dialog-content">
        <div class="import-info">
          <el-icon class="import-icon"><Upload /></el-icon>
          <div class="import-text">
            <h3>批量导入重命名规则</h3>
            <p>支持导入CSV格式的重命名规则，每行格式：原文件名,新文件名</p>
          </div>
        </div>
        
        <div class="import-methods">
          <el-tabs v-model="importMethod" type="border-card">
            <el-tab-pane label="文本输入" name="text">
              <div class="text-import">
                <el-input
                  v-model="batchImportRules"
                  type="textarea"
                  :rows="10"
                  placeholder="请输入重命名规则，每行格式：原文件名,新文件名&#10;例如：&#10;photo1.jpg,vacation_001.jpg&#10;photo2.jpg,vacation_002.jpg&#10;document.pdf,report_2025.pdf"
                />
                <div class="import-tips">
                  <div class="tip-item">
                    <el-icon><InfoFilled /></el-icon>
                    <span>每行一个重命名规则，用逗号分隔原文件名和新文件名</span>
                  </div>
                  <div class="tip-item">
                    <el-icon><Warning /></el-icon>
                    <span>确保原文件名与系统中的文件完全匹配</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="文件上传" name="file">
              <div class="file-import">
                <el-upload
                  ref="importUploadRef"
                  :auto-upload="false"
                  :show-file-list="true"
                  accept=".csv,.txt"
                  :on-change="handleImportFileChange"
                  drag
                >
                  <el-icon class="upload-icon"><UploadFilled /></el-icon>
                  <div class="upload-text">
                    <div class="upload-title">拖拽CSV文件到此处</div>
                    <div class="upload-subtitle">或 <em>点击选择文件</em></div>
                  </div>
                  <template #tip>
                    <div class="upload-tips">
                      <div class="tip-item">
                        <el-icon><InfoFilled /></el-icon>
                        <span>支持CSV和TXT格式文件</span>
                      </div>
                      <div class="tip-item">
                        <el-icon><Check /></el-icon>
                        <span>文件格式：原文件名,新文件名</span>
                      </div>
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="preview-section" v-if="importPreview.length > 0">
          <h4>导入预览 ({{ importPreview.length }} 条规则)</h4>
          <div class="preview-list">
            <div
              v-for="(rule, index) in importPreview.slice(0, 5)"
              :key="index"
              class="preview-item"
            >
              <div class="preview-original">
                <el-icon><Document /></el-icon>
                <span>{{ rule.original }}</span>
              </div>
              <div class="preview-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
              <div class="preview-new">
                <el-icon><Edit /></el-icon>
                <span>{{ rule.new }}</span>
              </div>
            </div>
            <div v-if="importPreview.length > 5" class="preview-more">
              还有 {{ importPreview.length - 5 }} 条规则...
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchImportDialog = false">取消</el-button>
          <el-button @click="parseImportRules" :disabled="!batchImportRules.trim()">
            <el-icon><View /></el-icon>
            预览规则
          </el-button>
          <el-button type="primary" @click="applyImportRules" :disabled="importPreview.length === 0">
            <el-icon><Check /></el-icon>
            应用规则 ({{ importPreview.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox, ElProgress } from 'element-plus'
import {
  Refresh,
  Plus,
  DataAnalysis,
  Document,
  TrendCharts,
  Select,
  Check,
  Star,
  View,
  Operation,
  Collection,
  Lightning,
  ArrowRight,
  Close,
  DocumentRemove,
  Setting,
  Edit,
  Files,
  EditPen,
  Picture,
  VideoPlay,
  InfoFilled,
  Warning,
  Download,
  CopyDocument,
  ArrowDown,
  ArrowUp,
  Search,
  MoreFilled,
  FolderOpened,
  DocumentCopy,
  Calendar,
  Clock,
  Bottom,
  Top,
  Upload
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize } from '@/utils/api'
import type { FileItem } from '@/types'

const authStore = useAuthStore()

// 响应式数据
const files = ref<FileItem[]>([])
const selectedFiles = ref<FileItem[]>([])
const loading = ref(false)
const renaming = ref(false)
const templates = ref<any[]>([])
const previewResults = ref<{ original: string; new: string }[]>([])
const showExportDialog = ref(false)
const showBatchImportDialog = ref(false)
const exportType = ref('zip')
const exporting = ref(false)
const batchImportRules = ref('')
const importMethod = ref('text')
const importPreview = ref<{ original: string; new: string }[]>([])

// 新增的过滤相关状态
const filterType = ref('')

// 性能优化相关
const batchSize = ref(10) // 批量处理大小
const processingProgress = ref(0) // 处理进度
const showProgress = ref(false) // 显示进度条
const isProcessing = ref(false) // 是否正在处理
const processedCount = ref(0) // 已处理数量
const totalCount = ref(0) // 总数量
const errorCount = ref(0) // 错误数量

// 批量收缩相关
const isAllExpanded = ref(false) // 是否全部展开

// 分页相关
const currentPage = ref(1)
const pageSize = ref(50) // 增加默认页面大小以减少分页次数

// 搜索相关
const searchKeyword = ref('')
const searchDebounceTimer = ref<number | null>(null)

// 重命名配置
const renameType = ref('regex')
const regexPattern = ref('')
const regexReplacement = ref('')
const prefixText = ref('')
const suffixText = ref('')
const findText = ref('')
const replaceText = ref('')
const baseName = ref('文件')
const startNumber = ref(1)
const numberPadding = ref(3)
const newExtension = ref('')
const caseType = ref('lower')

// 映射替换相关
const mappingFrom = ref('')
const mappingTo = ref('')

// 虚拟滚动相关
const virtualScrollEnabled = ref(false)
const visibleStartIndex = ref(0)
const visibleEndIndex = ref(50)
const itemHeight = 40 // 每行高度
const containerHeight = 400 // 容器高度
const visibleItemCount = Math.floor(containerHeight / itemHeight)

// 性能监控相关
const performanceMode = ref('auto') // auto, high, low
const renderTime = ref(0)
const lastRenderStart = ref(0)

// 映射替换输入防抖
const mappingInputDebounceTimer = ref<number | null>(null)

// 计算属性
const totalFiles = computed(() => files.value.length)
const previewCount = computed(() => previewResults.value.length)

// 搜索和分页计算属性
const filteredFiles = computed(() => {
  let result = files.value
  
  // 搜索过滤
  if (searchKeyword.value.trim()) {
    result = result.filter(file =>
      file.original_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  // 类型过滤
  if (filterType.value) {
    result = result.filter(file => {
      const ext = getFileExtension(file.original_name)
      switch (filterType.value) {
        case 'image':
          return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)
        case 'document':
          return ['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)
        case 'video':
          return ['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)
        default:
          return true
      }
    })
  }
  
  return result
})

// 智能分页大小计算
const smartPageSizes = computed(() => {
  const total = filteredFiles.value.length
  const baseSizes = [20, 50, 100]
  
  if (total <= 100) {
    // 小数据量：提供基础选项
    return baseSizes
  } else if (total <= 500) {
    // 中等数据量：添加200和500选项
    return [...baseSizes, 200, 500]
  } else if (total <= 1000) {
    // 大数据量：添加更多选项，包括"全部"
    return [...baseSizes, 200, 500, 1000, total]
  } else if (total <= 5000) {
    // 超大数据量：提供更灵活的选项
    return [...baseSizes, 200, 500, 1000, 2000, Math.ceil(total / 2), total]
  } else {
    // 海量数据：提供分层选项，避免性能问题
    const quarter = Math.ceil(total / 4)
    const half = Math.ceil(total / 2)
    return [...baseSizes, 200, 500, 1000, 2000, quarter, half, total]
  }
})

const paginatedFiles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFiles.value.slice(start, end)
})

// 工具函数
const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

const getFileNameWithoutExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(0, lastDot) : filename
}

const getFileIcon = (extension: string) => {
  const ext = extension?.toLowerCase() || ''
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
    return Picture
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)) {
    return VideoPlay
  }
  return Document
}

const getFileTypeTag = (extension: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    jpg: 'warning',
    jpeg: 'warning',
    png: 'warning',
    gif: 'warning',
    pdf: 'danger',
    doc: 'primary',
    docx: 'primary',
    txt: 'info',
    mp4: 'primary',
    mp3: 'success'
  }
  return typeMap[extension?.toLowerCase()] || 'info'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 解析映射列表函数
const parseMappingList = (text: string): string[] => {
  if (!text || !text.trim()) {
    return []
  }
  
  // 支持换行符和空格分隔
  return text.trim()
    .split(/[\n\s,]+/)
    .map(item => item.trim())
    .filter(item => item.length > 0)
}

// 文件操作
const loadFiles = async () => {
  try {
    loading.value = true
    // 移除文件数量限制，获取所有文件
    const response = await apiMethods.files.list({ limit: 1000 })
    files.value = response.data.data.files
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: FileItem[]) => {
  selectedFiles.value = selection
  debouncedInputUpdate()
}

const selectAllFiles = () => {
  selectedFiles.value = [...files.value]
  debouncedInputUpdate()
}

const clearSelection = () => {
  selectedFiles.value = []
  previewResults.value = []
}

// 切换文件选择状态
const toggleFileSelection = (file: FileItem) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file)
  }
  debouncedInputUpdate()
}

// 重命名逻辑
const generateNewName = (originalName: string, index: number): string => {
  const extension = getFileExtension(originalName)
  const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName

  switch (renameType.value) {
    case 'regex':
      if (regexPattern.value && regexReplacement.value) {
        try {
          const regex = new RegExp(regexPattern.value, 'g')
          const newName = nameWithoutExt.replace(regex, regexReplacement.value)
          return extension ? `${newName}.${extension}` : newName
        } catch (error) {
          return originalName
        }
      }
      return originalName

    case 'prefix':
      return extension ? `${prefixText.value}${nameWithoutExt}.${extension}` : `${prefixText.value}${nameWithoutExt}`

    case 'suffix':
      return extension ? `${nameWithoutExt}${suffixText.value}.${extension}` : `${nameWithoutExt}${suffixText.value}`

    case 'replace':
      if (findText.value) {
        const newName = nameWithoutExt.replace(new RegExp(findText.value, 'g'), replaceText.value)
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'mapping':
      if (mappingFrom.value && mappingTo.value) {
        const fromList = parseMappingList(mappingFrom.value)
        const toList = parseMappingList(mappingTo.value)
        
        // 检查数量是否匹配
        if (fromList.length !== toList.length) {
          return originalName
        }
        
        // 创建映射关系
        const mappingMap = new Map<string, string>()
        fromList.forEach((from, index) => {
          mappingMap.set(from, toList[index])
        })
        
        // 在文件名中查找并替换匹配的数字
        let newName = nameWithoutExt
        for (const [from, to] of mappingMap) {
          if (newName.includes(from)) {
            newName = newName.replace(new RegExp(from, 'g'), to)
            break // 只替换第一个匹配的映射
          }
        }
        
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'sequence':
      const number = (startNumber.value + index).toString().padStart(numberPadding.value, '0')
      return extension ? `${baseName.value}${number}.${extension}` : `${baseName.value}${number}`

    case 'extension':
      if (newExtension.value.trim()) {
        // 清理新扩展名，移除开头的点号（如果有的话）
        const cleanExtension = newExtension.value.trim().replace(/^\.+/, '')
        return cleanExtension ? `${nameWithoutExt}.${cleanExtension}` : nameWithoutExt
      }
      return originalName

    case 'case':
      switch (caseType.value) {
        case 'upper':
          return originalName.toUpperCase()
        case 'lower':
          return originalName.toLowerCase()
        case 'title':
          return originalName.toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
        default:
          return originalName
      }

    default:
      return originalName
  }
}

// 防抖优化的预览更新
const updatePreviewDebounceTimer = ref<number | null>(null)

// 虚拟滚动计算函数
const calculateVisibleRange = (scrollTop: number) => {
  const start = Math.floor(scrollTop / itemHeight)
  const end = Math.min(start + visibleItemCount + 5, selectedFiles.value.length) // 额外渲染5个项目作为缓冲
  visibleStartIndex.value = Math.max(0, start - 2) // 前面也保留2个缓冲
  visibleEndIndex.value = end
}

// 获取可见的文件列表
const visibleFiles = computed(() => {
  if (!virtualScrollEnabled.value || selectedFiles.value.length <= 100) {
    return selectedFiles.value
  }
  return selectedFiles.value.slice(visibleStartIndex.value, visibleEndIndex.value)
})

// 滚动事件处理
const handleScroll = (event: Event) => {
  if (!virtualScrollEnabled.value) return
  const target = event.target as HTMLElement
  calculateVisibleRange(target.scrollTop)
}

const updatePreview = () => {
  // 清除之前的防抖定时器
  if (updatePreviewDebounceTimer.value) {
    clearTimeout(updatePreviewDebounceTimer.value)
  }
  
  // 设置防抖延迟，减少频繁计算
  updatePreviewDebounceTimer.value = window.setTimeout(() => {
    if (selectedFiles.value.length === 0) {
      previewResults.value = []
      return
    }

    // 性能监控开始
    lastRenderStart.value = performance.now()

    // 启用虚拟滚动条件：文件数量超过100个
    virtualScrollEnabled.value = selectedFiles.value.length > 100
    
    // 根据文件数量动态调整预览限制
    const maxPreviewCount = selectedFiles.value.length > 200 ? 50 : 100
    const filesToPreview = virtualScrollEnabled.value ?
      visibleFiles.value.slice(0, maxPreviewCount) :
      selectedFiles.value.slice(0, maxPreviewCount)
    
    // 使用requestAnimationFrame优化渲染
    requestAnimationFrame(() => {
      previewResults.value = filesToPreview.map((file, index) => ({
        original: file.original_name,
        new: generateNewName(file.original_name, index)
      }))
      
      // 性能监控结束
      renderTime.value = performance.now() - lastRenderStart.value
      
      // 如果渲染时间过长，自动启用高性能模式
      if (renderTime.value > 100 && performanceMode.value === 'auto') {
        performanceMode.value = 'high'
        ElMessage.warning(`检测到渲染较慢(${renderTime.value.toFixed(1)}ms)，已自动启用高性能模式`)
      }
    })

    // 如果文件数量超过预览限制，显示提示
    if (selectedFiles.value.length > maxPreviewCount) {
      ElMessage.info(`预览显示前${maxPreviewCount}个文件，共选中${selectedFiles.value.length}个文件`)
    }
  }, 300) // 300ms防抖延迟
}

// 防抖搜索
const debouncedSearch = () => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  searchDebounceTimer.value = window.setTimeout(() => {
    handleSearch()
  }, 300)
}

// 统一的输入防抖处理
const debouncedInputUpdate = () => {
  if (updatePreviewDebounceTimer.value) {
    clearTimeout(updatePreviewDebounceTimer.value)
  }
  updatePreviewDebounceTimer.value = window.setTimeout(() => {
    updatePreview()
  }, 300) // 统一使用300ms防抖
}

// 映射替换输入防抖处理（使用更长的延迟）
const debouncedMappingUpdate = () => {
  if (mappingInputDebounceTimer.value) {
    clearTimeout(mappingInputDebounceTimer.value)
  }
  mappingInputDebounceTimer.value = window.setTimeout(() => {
    updatePreview()
  }, 500) // 映射替换使用更长的防抖时间，因为通常输入内容较多
}

const previewChanges = () => {
  debouncedInputUpdate()
  if (previewResults.value.length === 0) {
    ElMessage.warning('请先选择文件并配置重命名规则')
  }
}

// 下载重命名后的文件（不修改数据库）
const downloadRenamedFiles = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要重命名的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要下载重命名后的 ${selectedFiles.value.length} 个文件吗？`,
      '确认下载',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 初始化进度状态
    renaming.value = true
    isProcessing.value = true

    try {
      // 准备重命名请求数据
      const renameData = {
        file_ids: selectedFiles.value.map(file => file.id),
        type: renameType.value,
        pattern: regexPattern.value,
        replacement: regexReplacement.value,
        prefix: prefixText.value,
        suffix: suffixText.value,
        find_text: findText.value,
        replace_text: replaceText.value,
        base_name: baseName.value,
        start_number: startNumber.value,
        number_padding: numberPadding.value,
        new_extension: newExtension.value,
        mapping_from: mappingFrom.value,
        mapping_to: mappingTo.value
      }

      // 添加调试日志
      console.log('DEBUG: Sending rename request:', renameData)

      // 调用新的下载API（不修改数据库）
      const response = await fetch('/api/v1/rename/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(renameData)
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // 显示预览结果
          previewResults.value = result.data.previews.map((preview: any) => ({
            original: preview.original_name,
            new: preview.new_name
          }))
          
          // 构建文件名映射：文件ID -> 重命名后的文件名
          const fileNameMapping: Record<number, string> = {}
          result.data.previews.forEach((preview: any, index: number) => {
            const fileId = selectedFiles.value[index]?.id
            if (fileId && preview.new_name) {
              fileNameMapping[fileId] = preview.new_name
            }
          })
          
          // 使用重命名后的文件名下载ZIP
          await downloadSelectedAsZipWithRename(fileNameMapping)
          
          ElMessage.success(`成功生成 ${result.data.total_count} 个重命名文件的下载`)
        } else {
          ElMessage.error(result.message || '生成重命名文件失败')
        }
      } else {
        ElMessage.error('请求失败，请重试')
      }
    } catch (error) {
      console.error('下载重命名文件失败:', error)
      ElMessage.error('下载重命名文件失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量重命名失败:', error)
      ElMessage.error('批量重命名失败')
    }
  } finally {
    renaming.value = false
    isProcessing.value = false
  }
}

// 模板操作
const addTemplate = () => {
  ElMessage.info('模板功能开发中...')
}

const saveTemplate = () => {
  ElMessage.info('保存模板功能开发中...')
}

const applyCommonTemplate = () => {
  // 应用常用模板：添加日期前缀
  renameType.value = 'prefix'
  prefixText.value = new Date().toISOString().split('T')[0] + '_'
  debouncedInputUpdate()
  ElMessage.success('已应用日期前缀模板')
}

// 处理模板命令
const handleTemplateCommand = (command: string) => {
  switch (command) {
    case 'date-prefix':
      renameType.value = 'prefix'
      prefixText.value = new Date().toISOString().split('T')[0] + '_'
      debouncedInputUpdate()
      ElMessage.success('已应用日期前缀模板')
      break
    case 'timestamp':
      renameType.value = 'suffix'
      const now = new Date()
      suffixText.value = '_' + now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
      debouncedInputUpdate()
      ElMessage.success('已应用时间戳后缀模板')
      break
    case 'sequence':
      renameType.value = 'sequence'
      baseName.value = '文件_'
      startNumber.value = 1
      numberPadding.value = 3
      debouncedInputUpdate()
      ElMessage.success('已应用序号重命名模板')
      break
    case 'lowercase':
      renameType.value = 'regex'
      regexPattern.value = '(.*)'
      regexReplacement.value = '$1'
      // 这里需要特殊处理，将文件名转换为小写
      ElMessage.info('小写转换功能开发中')
      break
    case 'uppercase':
      renameType.value = 'regex'
      regexPattern.value = '(.*)'
      regexReplacement.value = '$1'
      // 这里需要特殊处理，将文件名转换为大写
      ElMessage.info('大写转换功能开发中')
      break
    case 'import':
      showBatchImportDialog.value = true
      break
  }
}

// 导出相关函数
const exportFiles = async () => {
  try {
    exporting.value = true
    
    if (exportType.value === 'individual') {
      // 逐个下载文件
      for (const file of selectedFiles.value) {
        try {
          const response = await apiMethods.files.download(file.id)
          // 创建下载链接
          const blob = new Blob([response.data])
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = file.original_name
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        } catch (error) {
          console.error(`下载文件 ${file.original_name} 失败:`, error)
        }
      }
      ElMessage.success('文件导出完成')
    } else {
      // ZIP打包下载
      try {
        const fileIds = selectedFiles.value.map(file => file.id)
        const response = await apiMethods.files.downloadZip(fileIds)
        
        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `批量重命名文件_${new Date().toISOString().split('T')[0]}.zip`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('ZIP文件下载完成')
      } catch (error) {
        console.error('ZIP下载失败:', error)
        ElMessage.error('ZIP下载失败，请重试')
      }
    }
    
    showExportDialog.value = false
    clearSelection()
  } catch (error) {
    console.error('导出文件失败:', error)
    ElMessage.error('导出文件失败')
  } finally {
    exporting.value = false
  }
}

// ZIP下载相关函数
const downloadSelectedAsZip = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件')
    return
  }

  try {
    exporting.value = true
    const fileIds = selectedFiles.value.map(file => file.id)
    const response = await apiMethods.files.downloadZip(fileIds)
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `选中文件_${new Date().toISOString().split('T')[0]}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('ZIP文件下载完成')
  } catch (error) {
    console.error('ZIP下载失败:', error)
    ElMessage.error('ZIP下载失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 使用重命名后的文件名下载ZIP
const downloadSelectedAsZipWithRename = async (fileNameMapping: Record<number, string>) => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件')
    return
  }

  try {
    exporting.value = true
    const fileIds = selectedFiles.value.map(file => file.id)
    
    // 添加调试日志
    console.log('DEBUG: downloadSelectedAsZipWithRename called with:', {
      fileIds,
      fileNameMapping,
      selectedFiles: selectedFiles.value.map(f => ({ id: f.id, name: f.original_name }))
    })
    
    // 调用支持重命名的ZIP下载API
    const response = await fetch('/api/v1/files/download-zip', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        file_ids: fileIds,
        file_names: fileNameMapping
      })
    })

    if (!response.ok) {
      throw new Error('下载请求失败')
    }

    // 创建下载链接
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `重命名文件_${new Date().toISOString().split('T')[0]}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('重命名文件ZIP下载完成')
  } catch (error) {
    console.error('重命名ZIP下载失败:', error)
    ElMessage.error('重命名ZIP下载失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 批量导入相关函数
const handleImportFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    batchImportRules.value = content
    parseImportRules()
  }
  reader.readAsText(file.raw)
}

const parseImportRules = () => {
  if (!batchImportRules.value.trim()) {
    importPreview.value = []
    return
  }

  const lines = batchImportRules.value.trim().split('\n')
  const rules: { original: string; new: string }[] = []

  for (const line of lines) {
    const trimmedLine = line.trim()
    if (!trimmedLine) continue

    const parts = trimmedLine.split(',')
    if (parts.length >= 2) {
      const original = parts[0].trim()
      const newName = parts.slice(1).join(',').trim()
      if (original && newName) {
        rules.push({ original, new: newName })
      }
    }
  }

  importPreview.value = rules
  if (rules.length > 0) {
    ElMessage.success(`成功解析 ${rules.length} 条重命名规则`)
  } else {
    ElMessage.warning('未找到有效的重命名规则')
  }
}

const applyImportRules = async () => {
  if (importPreview.value.length === 0) {
    ElMessage.warning('没有可应用的重命名规则')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要应用 ${importPreview.value.length} 条重命名规则吗？`,
      '确认批量重命名',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    renaming.value = true
    let successCount = 0
    let errorCount = 0

    // 找到匹配的文件并重命名
    for (const rule of importPreview.value) {
      const file = files.value.find(f => f.original_name === rule.original)
      if (file) {
        try {
          await apiMethods.files.update(file.id, { new_name: rule.new })
          successCount++
        } catch (error) {
          console.error(`重命名文件 ${rule.original} 失败:`, error)
          errorCount++
        }
      } else {
        errorCount++
        console.warn(`未找到文件: ${rule.original}`)
      }
    }

    if (successCount > 0) {
      ElMessage.success(`成功重命名 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个失败` : ''}`)
      await loadFiles()
      showBatchImportDialog.value = false
      batchImportRules.value = ''
      importPreview.value = []
    } else {
      ElMessage.error('没有文件被重命名，请检查文件名是否匹配')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量重命名失败:', error)
      ElMessage.error('批量重命名失败')
    }
  } finally {
    renaming.value = false
  }
}

// 复制功能
const copyFileName = async (filename: string) => {
  try {
    await navigator.clipboard.writeText(filename)
    ElMessage.success('文件名已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

const copyFileNameWithoutExtension = async (filename: string) => {
  try {
    const nameWithoutExt = getFileNameWithoutExtension(filename)
    await navigator.clipboard.writeText(nameWithoutExt)
    ElMessage.success('文件名（无后缀）已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 分页处理函数
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
}

// 搜索功能
const handleSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
}

// 批量复制功能
const handleBatchCopy = async (command: string) => {
  try {
    let fileNames: string[] = []
    
    switch (command) {
      case 'all-with-ext':
        fileNames = files.value.map(file => file.original_name)
        break
      case 'all-without-ext':
        fileNames = files.value.map(file => getFileNameWithoutExtension(file.original_name))
        break
      case 'selected-with-ext':
        fileNames = selectedFiles.value.map(file => file.original_name)
        break
      case 'selected-without-ext':
        fileNames = selectedFiles.value.map(file => getFileNameWithoutExtension(file.original_name))
        break
      default:
        return
    }
    
    if (fileNames.length === 0) {
      ElMessage.warning('没有文件可复制')
      return
    }
    
    // 将文件名用换行符连接
    const textToCopy = fileNames.join('\n')
    await navigator.clipboard.writeText(textToCopy)
    
    const typeText = command.includes('selected') ? '选中' : '所有'
    const extText = command.includes('without') ? '（无后缀）' : '（含后缀）'
    ElMessage.success(`已复制 ${fileNames.length} 个${typeText}文件名${extText}到剪贴板`)
  } catch (error) {
    console.error('批量复制失败:', error)
    ElMessage.error('批量复制失败，请重试')
  }
}

// 优化的搜索输入处理
const handleSearchInput = () => {
  debouncedSearch()
}

// 批量收缩功能
const toggleExpandAll = () => {
  if (isAllExpanded.value) {
    // 收缩所有：清空选择
    selectedFiles.value = []
    isAllExpanded.value = false
    ElMessage.info('已收缩所有文件选择')
  } else {
    // 展开所有：选择所有文件
    selectedFiles.value = [...files.value]
    isAllExpanded.value = true
    ElMessage.success(`已展开选择所有 ${files.value.length} 个文件`)
  }
  debouncedInputUpdate()
}

// 处理全选/取消全选
const handleSelectAll = (val: any) => {
  if (val) {
    selectedFiles.value = [...paginatedFiles.value]
  } else {
    selectedFiles.value = []
  }
  debouncedInputUpdate()
}

// 获取文件图标颜色
const getFileIconColor = (extension: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#ff4d4f',
    doc: '#1890ff',
    docx: '#1890ff',
    xls: '#52c41a',
    xlsx: '#52c41a',
    ppt: '#fa8c16',
    pptx: '#fa8c16',
    txt: '#8c8c8c',
    jpg: '#722ed1',
    jpeg: '#722ed1',
    png: '#722ed1',
    gif: '#722ed1',
    bmp: '#722ed1',
    svg: '#722ed1',
    mp4: '#eb2f96',
    avi: '#eb2f96',
    mov: '#eb2f96',
    wmv: '#eb2f96',
    mp3: '#13c2c2',
    wav: '#13c2c2',
    flac: '#13c2c2',
    zip: '#faad14',
    rar: '#faad14',
    '7z': '#faad14',
    tar: '#faad14',
    gz: '#faad14'
  }
  return colorMap[extension.toLowerCase()] || '#8c8c8c'
}

// 获取相对时间
const getRelativeTime = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`

  return date.toLocaleDateString('zh-CN')
}

// 高亮显示文件名差异
const highlightDifferences = (original: string, newName: string, type: 'original' | 'new') => {
  if (original === newName) {
    return original
  }

  // 简单的差异检测算法
  const text = type === 'original' ? original : newName
  const other = type === 'original' ? newName : original

  // 如果完全不同，高亮整个文件名
  if (text.length === 0 || other.length === 0) {
    return `<span class="diff-highlight ${type === 'new' ? 'diff-added' : 'diff-removed'}">${text}</span>`
  }

  // 找到公共前缀和后缀
  let prefixEnd = 0
  let suffixStart = text.length

  // 找公共前缀
  while (prefixEnd < text.length && prefixEnd < other.length &&
         text[prefixEnd] === other[prefixEnd]) {
    prefixEnd++
  }

  // 找公共后缀
  let textSuffixPos = text.length - 1
  let otherSuffixPos = other.length - 1
  while (textSuffixPos >= prefixEnd && otherSuffixPos >= prefixEnd &&
         text[textSuffixPos] === other[otherSuffixPos]) {
    textSuffixPos--
    otherSuffixPos--
  }
  suffixStart = textSuffixPos + 1

  // 构建高亮结果
  const prefix = text.substring(0, prefixEnd)
  const middle = text.substring(prefixEnd, suffixStart)
  const suffix = text.substring(suffixStart)

  if (middle.length === 0) {
    return text
  }

  const highlightClass = type === 'new' ? 'diff-added' : 'diff-removed'
  return `${prefix}<span class="diff-highlight ${highlightClass}">${middle}</span>${suffix}`
}

onMounted(() => {
  loadFiles()
})
</script>

<style scoped>
.batch-rename-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 批量重命名横幅 - 简约现代风格 */
.batch-rename-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.batch-rename-banner .banner-title,
.batch-rename-page .banner-title,
h1.banner-title,
.batch-rename-banner h1,
.batch-rename-page h1,
.banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.95rem;
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.banner-visual {
  position: relative;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-rename-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.batch-rename-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .batch-rename-banner:hover .batch-rename-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 区块标题 */
.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 32px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .el-icon {
  color: #3b82f6;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  max-width: 100%;
}

.stats-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 8px 10px !important;
  position: relative !important;
  overflow: visible !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  min-height: 60px !important;
  border-left: 3px solid transparent !important;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stats-card.primary {
  border-left-color: #3b82f6;
}

.stats-card.success {
  border-left-color: #10b981;
}

.stats-card.warning {
  border-left-color: #f59e0b;
}

.stats-card.info {
  border-left-color: #06b6d4;
}

.stats-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stats-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stats-card.warning:hover {
  border-left-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.stats-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

.stats-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.stats-card.primary .stats-icon {
  background: #3b82f6;
}

.stats-card.success .stats-icon {
  background: #10b981;
}

.stats-card.warning .stats-icon {
  background: #f59e0b;
}

.stats-card.info .stats-icon {
  background: #06b6d4;
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.stats-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.stats-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stats-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stats-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 1px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.625rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stats-trend .el-icon {
  font-size: 0.625rem;
}

/* 主要工作区域 */
.main-workspace {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 24px;
  margin-bottom: 40px;
  align-items: stretch; /* 确保两个容器拉伸到相同高度 */
}

/* 文件选择卡片增强样式 */
.file-selection-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.file-selection-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
  padding: 20px 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 增强的工具栏样式 */
.enhanced-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  margin: -24px -24px 20px -24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 0;
  gap: 16px;
}

.search-box {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 增强的文件表格样式 */
.enhanced-file-table {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.enhanced-file-table .table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  font-size: 13px;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.enhanced-file-table .table-body {
  max-height: 450px;
  overflow-y: auto;
  background: white;
}

/* 文件信息增强样式 */
.file-info-enhanced {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 4px 0;
}

.file-icon-box {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.enhanced-file-table .table-row:hover .file-icon-box {
  background: #e2e8f0;
  transform: scale(1.05);
}

.file-icon-box .el-icon {
  font-size: 20px;
}

.file-ext-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  padding: 1px 4px;
  background: #3b82f6;
  color: white;
  font-size: 9px;
  font-weight: 700;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name-text {
  display: block;
  font-weight: 500;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta-info {
  display: block;
  font-size: 12px;
  color: #64748b;
}

.size-text {
  font-weight: 500;
  color: #475569;
  font-size: 13px;
}

/* 表格行悬停效果 */
.enhanced-file-table .table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.enhanced-file-table .table-row:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  transform: translateX(2px);
}

.enhanced-file-table .table-row.selected {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-left: 3px solid #3b82f6;
}

/* 自定义滚动条 */
.enhanced-file-table .table-body::-webkit-scrollbar {
  width: 8px;
}

.enhanced-file-table .table-body::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.enhanced-file-table .table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.enhanced-file-table .table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 重命名规则卡片样式 */
.rename-rules-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 24px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.rename-rules-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 快速操作按钮组 */
.quick-rename-actions {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.quick-rename-actions .el-button-group {
  width: 100%;
  display: flex;
}

.quick-rename-actions .el-button {
  flex: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.quick-rename-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-rename-actions .el-button.el-button--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 增强的表单样式 */
.rule-form-modern {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group-modern {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-label-modern {
  font-weight: 600;
  color: #1e293b;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-label-modern .el-icon {
  color: #3b82f6;
  font-size: 14px;
}

/* 现代化输入框样式 */
.modern-select :deep(.el-input__wrapper),
.modern-input :deep(.el-input__wrapper),
.modern-number-input :deep(.el-input__wrapper) {
  border-radius: 10px;
  border: 2px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-select:hover :deep(.el-input__wrapper),
.modern-input:hover :deep(.el-input__wrapper),
.modern-number-input:hover :deep(.el-input__wrapper) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-select :deep(.el-input__wrapper.is-focus),
.modern-input :deep(.el-input__wrapper.is-focus),
.modern-number-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 规则配置区域样式 */
.rule-config-modern {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-top: 16px;
  gap: 16px;
  display: flex;
  flex-direction: column;
}

.input-hint {
  font-size: 11px;
  color: #64748b;
  margin-top: 4px;
  padding-left: 4px;
  line-height: 1.4;
}

/* 映射状态样式 */
.mapping-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #1e293b;
}

.status-item .el-icon {
  font-size: 14px;
  color: #3b82f6;
}

.status-item.status-warning {
  color: #f59e0b;
}

.status-item.status-warning .el-icon {
  color: #f59e0b;
}

/* 操作按钮增强样式 */
.form-actions {
  margin-top: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.form-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.action-buttons-container {
  display: flex;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.elegant-action-btn {
  flex: 1;
  height: 48px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.primary-action {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.primary-action:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.secondary-action {
  background: white;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.secondary-action:hover {
  background: #f9fafb;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.workspace-left,
.workspace-right {
  min-height: 200px; /* 增加到200px，比原来的150px增高约33% */
  max-height: 600px; /* 增加最大高度到600px，比原来的400px增高50% */
  display: flex;
  flex-direction: column;
}

/* 左侧文件选择容器 - 支持滚动 */
.workspace-left .section-card {
  display: flex;
  flex-direction: column;
  height: 100%; /* 占满父容器高度 */
}

.workspace-left .section-content {
  flex: 1;
  overflow: hidden; /* 隐藏溢出内容 */
  display: flex;
  flex-direction: column;
}

.workspace-left .file-list-container {
  flex: 1;
  overflow-y: auto; /* 允许垂直滚动 */
  min-height: 0; /* 允许收缩 */
}

/* 右侧重命名规则容器 - 与左侧等高 */
.workspace-right .section-card {
  display: flex;
  flex-direction: column;
  height: 100%; /* 占满父容器高度 */
}

.workspace-right .section-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 如果内容过多也支持滚动 */
}

.workspace-right .section-card {
  position: sticky;
  top: 24px;
}

.section-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.section-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.section-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.section-title .el-icon {
  color: #3b82f6;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-content {
  padding: 24px;
  background: white;
  position: relative;
}

/* 文件选择区域 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 48px;
  height: 48px;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #64748b;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.empty-content p {
  margin: 0 0 16px 0;
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.4;
}

.file-list-container {
  margin: -8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-size: 0.875rem;
  color: var(--text-primary);
  word-break: break-all;
}

.file-size {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 增强的文件信息样式 */
.file-info-enhanced {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.file-icon-wrapper {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.file-icon-wrapper:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
  border-color: var(--primary);
  transform: scale(1.05);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name-row {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.file-name-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  word-break: break-all;
  line-height: 1.4;
}

.file-extension {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 400;
  opacity: 0.8;
}

.file-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-info-enhanced:hover .file-actions {
  opacity: 1;
}

.copy-btn {
  font-size: 0.75rem;
  padding: 2px 6px;
  height: auto;
  min-height: 20px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-size-enhanced {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.file-type-tag {
  font-weight: 500;
  border-radius: 6px;
  font-size: 0.75rem;
}

/* 简洁高效的表格样式 */
.simple-file-table {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-primary);
  background: var(--bg-card);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}

.simple-file-table:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
  display: grid;
  grid-template-columns: 50px 1fr 100px 80px 80px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid var(--border-primary);
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-body {
  max-height: 500px; /* 增加高度以显示更多文件 */
  overflow-y: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  /* 减少重绘 */
  will-change: scroll-position;
  /* 虚拟滚动容器 */
  position: relative;
  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 自定义滚动条样式 */
.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.virtual-scroll-container {
  position: relative;
}

.virtual-scroll-spacer {
  height: 0;
  pointer-events: none;
}

.virtual-scroll-content {
  position: relative;
  transform: translateY(0);
}

.table-row {
  display: grid;
  grid-template-columns: 50px 1fr 100px 80px 80px;
  border-bottom: 1px solid var(--border-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.table-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: transparent;
  transition: background 0.3s ease;
}

.table-row:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.02) 100%);
  transform: translateX(2px);
}

.table-row:hover::before {
  background: var(--el-color-primary);
}

.table-row.selected {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.04) 100%);
  border-color: var(--el-color-primary);
  transform: translateX(2px);
}

.table-row.selected::before {
  background: var(--el-color-primary);
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell,
.table-cell {
  padding: 6px 8px; /* 减少垂直内边距，从12px减少到6px */
  display: flex;
  align-items: center;
  border-right: 1px solid var(--border-secondary);
  font-size: 13px; /* 减小字体以提高密度 */
  line-height: 1.3; /* 减小行高 */
}

.header-cell:last-child,
.table-cell:last-child {
  border-right: none;
}

.checkbox-cell {
  justify-content: center;
}

.name-cell {
  min-width: 0;
}

.size-cell,
.index-cell,
.action-cell {
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.file-icon {
  flex-shrink: 0;
  color: var(--el-color-primary);
  font-size: 14px; /* 减小图标尺寸 */
}

.file-name {
  font-size: 12px; /* 减小文件名字体 */
  color: var(--text-primary);
  word-break: break-all;
  line-height: 1.2; /* 减小行高 */
}

.file-size {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.file-index {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 600;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--el-color-primary);
  color: white;
  transform: scale(1.1);
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table-header,
  .table-row {
    grid-template-columns: 40px 1fr 80px 60px 60px;
  }
  
  .header-cell,
  .table-cell {
    padding: 4px 4px; /* 移动端进一步减少内边距 */
    font-size: 12px;
  }
  
  .file-name {
    font-size: 13px;
  }
}

/* 优化的文件列表样式（保留原有的卡片样式作为备用） */
.modern-file-grid {
  display: flex;
  flex-direction: column;
  gap: 15px; /* 增加间距，比原来的12px增加25% */
  padding: 10px; /* 增加内边距，比原来的8px增加25% */
}

.file-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 6px 10px; /* 进一步减少内边距 */
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 45px; /* 进一步减少高度 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.file-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.file-card:hover::before {
  opacity: 1;
}

.file-card.selected {
  border-color: var(--el-color-primary);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 51, 234, 0.08) 100%);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
}

.file-card-content {
  display: flex;
  align-items: center;
  gap: 8px; /* 减小间距 */
  flex: 1;
  min-width: 0;
}

.file-icon-modern {
  flex-shrink: 0;
  position: relative;
}

.file-icon-modern .el-icon {
  width: 48px; /* 减小图标尺寸 */
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px; /* 减小圆角 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px; /* 减小字体 */
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.file-card:hover .file-icon-modern .el-icon {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.35);
}

.file-type-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--el-color-primary);
  color: white;
  font-size: 10px; /* 减小字体大小 */
  font-weight: 700;
  padding: 2px 6px; /* 减小内边距 */
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.file-info-modern {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px; /* 减小间距 */
}

.file-name-modern {
  font-size: 15px; /* 减小字体 */
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.3; /* 减小行高 */
  display: flex;
  align-items: baseline;
  gap: 3px;
  word-break: break-all;
}

.name-part {
  color: var(--el-text-color-primary);
  flex: 1;
  min-width: 0;
}

.ext-part {
  color: var(--el-color-primary);
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px; /* 减小间距 */
  font-size: 13px; /* 减小字体大小 */
  color: var(--el-text-color-secondary);
}

.file-size-modern {
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
  padding: 2px 8px; /* 减小内边距 */
  border-radius: 6px;
  color: var(--el-color-primary);
  font-size: 12px; /* 明确设置更小的字体 */
}

.file-index {
  background: rgba(0, 0, 0, 0.08);
  padding: 2px 8px; /* 减小内边距 */
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px; /* 减小字体大小 */
  color: var(--el-text-color-regular);
}

.file-card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(10px);
}

.file-card:hover .file-card-actions {
  opacity: 1;
  transform: translateX(0);
}

.file-card.selected .file-card-actions {
  opacity: 1;
  transform: translateX(0);
}

.file-checkbox {
  transition: all 0.3s ease;
  transform: scale(0.9);
}

.file-card.selected .file-checkbox,
.file-card:hover .file-checkbox {
  opacity: 1;
  transform: scale(1);
}

.action-trigger {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(102, 126, 234, 0.2);
  font-size: 14px;
  color: var(--el-color-primary);
  transition: all 0.3s ease;
}

.action-trigger:hover {
  background: var(--el-color-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.selection-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--el-color-success);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-card.selected .selection-indicator {
  opacity: 1;
  transform: scale(1);
}

.selected-icon {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* 响应式文件网格 */
@media (max-width: 768px) {
  .modern-file-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .file-card {
    padding: 16px;
    height: 130px; /* 移动端稍微减小高度 */
  }
}

@media (max-width: 480px) {
  .modern-file-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .file-card {
    padding: 12px;
    height: 120px; /* 小屏幕进一步减小高度 */
  }
  
  .file-icon-modern .el-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .file-name-modern {
    font-size: 13px;
    -webkit-line-clamp: 2;
    max-height: 2.6em;
  }
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  margin-top: 16px;
  border-top: 1px solid var(--border-primary);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 0 0 8px 8px;
}

.pagination-wrapper :deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: var(--text-primary);
  --el-pagination-border-radius: 6px;
}

.pagination-wrapper :deep(.el-pagination .btn-next),
.pagination-wrapper :deep(.el-pagination .btn-prev) {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.el-pagination .btn-next:hover),
.pagination-wrapper :deep(.el-pagination .btn-prev:hover) {
  background: var(--primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper :deep(.el-pager li) {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.el-pager li:hover) {
  background: var(--primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper :deep(.el-pager li.is-active) {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* 重命名规则配置 */
.rule-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.rule-config {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少间距，从16px减少到12px */
}

.form-actions {
  margin-top: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.form-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.action-buttons-container {
  display: flex;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.elegant-action-btn {
  flex: 1;
  height: 52px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.elegant-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.elegant-action-btn:hover::before {
  left: 100%;
}

.primary-action {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.primary-action:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.primary-action:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.secondary-action {
  background: white;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.secondary-action:hover {
  background: #f9fafb;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.secondary-action:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}


.elegant-action-btn .el-icon {
  font-size: 16px;
}

.elegant-action-btn span {
  font-weight: 600;
  letter-spacing: 0.3px;
}

.elegant-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.elegant-action-btn:disabled::before {
  display: none;
}

/* 响应式按钮设计 */
@media (max-width: 768px) {
  .action-buttons-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .elegant-action-btn {
    height: 48px;
    font-size: 14px;
  }
  
  
  .form-actions {
    padding: 20px;
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .elegant-action-btn {
    height: 44px;
    font-size: 13px;
  }
  
  .elegant-action-btn .el-icon {
    font-size: 14px;
  }
  
  .form-actions {
    padding: 16px;
    margin-top: 16px;
  }
  
}

/* 预览结果 */
.preview-section {
  margin-bottom: 40px;
}

.preview-content {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  overflow: hidden;
}

.preview-list {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-primary);
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-original,
.preview-new {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  font-size: 0.875rem;
}

.preview-original {
  color: var(--text-secondary);
}

.preview-new {
  color: var(--text-primary);
  font-weight: 500;
}

.preview-new.has-changes {
  color: #0ea5e9;
}

.preview-arrow {
  color: var(--primary);
  font-size: 1.25rem;
}

/* 差异高亮样式 */
.diff-highlight {
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

.diff-added {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.diff-removed {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* 示例按钮样式 */
.regex-examples {
  margin-top: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.example-title {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 8px;
}

.example-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.example-items .el-button {
  font-size: 11px;
  padding: 4px 8px;
  height: auto;
  border-radius: 6px;
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.example-items .el-button:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-workspace {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .workspace-left,
  .workspace-right {
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .batch-rename-page {
    padding: 16px;
  }
  
  .batch-rename-banner {
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 12px;
  }

  .banner-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .banner-title {
    font-size: 1.125rem !important;
  }

  .banner-subtitle {
    font-size: 0.875rem;
  }

  .banner-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .header-card {
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 12px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .card-title {
    font-size: 1.125rem !important;
  }

  .card-subtitle {
    font-size: 0.875rem;
  }

  .header-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .stats-card {
    min-height: 55px;
    padding: 10px;
    gap: 10px;
  }
  
  .stats-icon {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
  
  .stats-icon .el-icon {
    font-size: 0.875rem !important;
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
  
  .stats-value {
    font-size: 1.25rem;
  }
  
  .stats-label {
    font-size: 0.75rem;
  }
  
  .stats-trend {
    font-size: 0.65rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .section-content {
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .preview-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .batch-rename-page {
    padding: 12px;
  }
  
  .page-title {
    font-size: 1.25rem !important;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .header-actions .el-button {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .stats-card {
    padding: 8px;
    gap: 8px;
    min-height: 50px;
  }
  
  .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .stats-icon .el-icon {
    font-size: 0.75rem !important;
    width: 0.75rem !important;
    height: 0.75rem !important;
  }
  
  .stats-value {
    font-size: 1.1rem;
  }
  
  .stats-label {
    font-size: 0.7rem;
  }
  
  .stats-trend {
    font-size: 0.6rem;
  }
  
  .section-header {
    padding: 12px 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .section-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .section-actions .el-button {
    width: 100%;
  }
}

/* 文件工具栏样式 */
.file-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  margin: -24px -24px 16px -24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--border-primary);
  border-radius: 16px 16px 0 0;
}

.file-stats {
  display: flex;
  align-items: center;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-item .el-icon {
  color: var(--primary);
  font-size: 1rem;
}

.file-search {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-search .el-input {
  --el-input-border-radius: 8px;
  --el-input-bg-color: var(--bg-card);
  --el-input-border-color: var(--border-primary);
}

.file-search .el-input:hover {
  --el-input-border-color: var(--primary);
}

.file-search .el-input.is-focus {
  --el-input-border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 响应式文件工具栏 */
@media (max-width: 768px) {
  .file-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .file-stats {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .file-search {
    width: 100%;
  }
  
  .file-search .el-input {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  .file-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stat-item {
    font-size: 0.8rem;
  }
}

/* 上传对话框样式 */
.upload-dialog-content {
  padding: 20px 0;
}

.upload-area {
  margin-bottom: 20px;
}

.modern-upload {
  .el-upload {
    width: 100%;
  }
  
  .el-upload-dragger {
    width: 100%;
    height: 200px;
    border: 2px dashed var(--el-border-color-lighter);
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary);
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
  opacity: 0.8;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.upload-subtitle {
  font-size: 14px;
  color: var(--el-text-color-regular);
  
  em {
    color: var(--el-color-primary);
    font-style: normal;
    font-weight: 500;
  }
}

.upload-tips {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .el-icon {
    margin-right: 8px;
    color: var(--el-color-primary);
  }
}

/* 导出对话框样式 */
.export-dialog-content {
  padding: 20px 0;
}

.export-info {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.export-icon {
  font-size: 40px;
  color: var(--el-color-primary);
  margin-right: 16px;
  opacity: 0.8;
}

.export-text {
  flex: 1;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.export-options {
  padding: 0 20px;
  
  .el-radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .el-radio {
    margin: 0;
    padding: 12px 16px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    background: var(--el-bg-color);
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
    
    &.is-checked {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 批量导入对话框样式 */
.batch-import-dialog-content {
  padding: 20px 0;
}

.import-info {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.import-icon {
  font-size: 40px;
  color: var(--el-color-primary);
  margin-right: 16px;
  opacity: 0.8;
}

.import-text {
  flex: 1;
}

.import-text h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.import-text p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.import-methods {
  margin-bottom: 24px;
}

.text-import {
  padding: 16px 0;
}

.import-tips {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.file-import {
  padding: 16px 0;
}

.preview-section {
  margin-top: 24px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.preview-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-more {
  text-align: center;
  padding: 12px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
  background: var(--el-bg-color);
  border-radius: 6px;
  margin-top: 8px;
}

/* 进度条样式 */
.progress-section {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.progress-stats {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-primary);
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 13px;
  font-weight: 500;
}

.success-count {
  color: var(--el-color-success);
}

.error-count {
  color: var(--el-color-danger);
}

.remaining-count {
  color: var(--el-text-color-secondary);
}

/* 进度条动画优化 */
.progress-section :deep(.el-progress-bar__outer) {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
}

.progress-section :deep(.el-progress-bar__inner) {
  border-radius: 8px;
  transition: width 0.3s ease;
}

/* 响应式进度条 */
@media (max-width: 768px) {
  .progress-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
/* 现代化重命名规则配置 */
.rule-form-modern {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.form-group-modern {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-label-modern {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.form-label-modern .el-icon {
  color: var(--el-color-primary);
  font-size: 16px;
}

.modern-select {
  --el-select-border-radius: 12px;
  --el-select-border-color-hover: var(--el-color-primary);
}

.modern-select :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modern-select :deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-select :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  width: 100%;
}

.option-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.option-text {
  flex: 1;
  min-width: 0;
}

.option-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 13px; /* 减小标签字体 */
  margin-bottom: 4px; /* 增加标签与输入框的间距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.option-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义下拉选择器样式 */
.modern-select :deep(.el-select-dropdown) {
  min-width: 350px !important;
  width: auto !important;
}

.modern-select :deep(.el-select-dropdown__item) {
  height: auto !important;
  min-height: 50px !important;
  padding: 8px 12px !important;
  line-height: normal !important;
  overflow: visible !important;
}

.custom-option {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  width: 100% !important;
  padding: 8px 0 !important;
  min-height: 50px !important;
}

.option-icon {
  font-size: 18px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.option-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  min-width: 0 !important;
}

.option-title {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #303133 !important;
  line-height: 1.4 !important;
  margin-bottom: 2px !important;
  white-space: nowrap !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.option-desc {
  font-size: 12px !important;
  color: #909399 !important;
  line-height: 1.3 !important;
  white-space: nowrap !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.form-row-modern {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.rule-config-modern {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减少间距，从20px减少到16px */
  padding: 20px; /* 减少内边距，从24px减少到20px */
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  margin-top: 20px;
}

.modern-input {
  --el-input-border-radius: 12px;
}

.modern-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: white;
}

.modern-input :deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.modern-number-input {
  width: 100%;
  --el-input-border-radius: 12px;
}

.modern-number-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: white;
}

.modern-number-input :deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-number-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.input-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  padding-left: 4px;
  line-height: 1.4;
}

/* 现代化文件工具栏增强样式 */
.file-toolbar {
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 50%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(102, 126, 234, 0.15);
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.file-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.file-stats {
  position: relative;
  z-index: 1;
}

.stat-item {
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 16px;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
}

.file-search {
  position: relative;
  z-index: 1;
}

.file-search .el-input {
  --el-input-border-radius: 12px;
  --el-input-bg-color: rgba(255, 255, 255, 0.8);
  --el-input-border-color: rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(8px);
}

.file-search .el-input :deep(.el-input__wrapper) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-search .el-input:hover :deep(.el-input__wrapper) {
  --el-input-border-color: var(--el-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.file-search .el-input.is-focus :deep(.el-input__wrapper) {
  --el-input-border-color: var(--el-color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* 响应式表单 */
@media (max-width: 768px) {
  .form-row-modern {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .rule-config-modern {
    padding: 14px; /* 移动端进一步减少内边距 */
    gap: 12px; /* 移动端减少间距 */
  }
  
  .file-toolbar::before {
    opacity: 0.5;
  }
  
  .stat-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* 映射替换状态样式 */
.mapping-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.status-item .el-icon {
  font-size: 16px;
  color: var(--el-color-primary);
}

.status-item.status-warning {
  color: var(--el-color-warning);
}

.status-item.status-warning .el-icon {
  color: var(--el-color-warning);
}

/* 映射替换输入框样式增强 */
.modern-input[type="textarea"] :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 2px solid transparent;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.modern-input[type="textarea"]:hover :deep(.el-textarea__inner) {
  border-color: var(--el-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-input[type="textarea"]:focus-within :deep(.el-textarea__inner) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* 响应式映射状态 */
@media (max-width: 768px) {
  .mapping-status {
    padding: 12px;
    gap: 6px;
  }
  
  .status-item {
    font-size: 12px;
  }
  
  .status-item .el-icon {
    font-size: 14px;
  }
}
}
</style>
/* 性能提示样式 */
.performance-tip {
  margin: 16px 0;
  padding: 0 16px;
}

.performance-tip .el-alert {
  border-radius: 8px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #90caf9;
}

.performance-tip .el-alert__title {
  font-weight: 600;
  color: #1976d2;
}

.performance-tip .el-alert__description {
  color: #424242;
  font-size: 13px;
}
/* 文件图标优化样式 */
.file-icon {
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: white !important;
  text-transform: uppercase !important;
  background: linear-gradient(135deg, #718096 0%, #4a5568 100%) !important;
  flex-shrink: 0 !important;
}

/* 文件名优化样式 */
.file-name {
  font-weight: 500 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 文件类型特定图标颜色 */
.file-info .file-icon[data-type="pdf"] {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
}

.file-info .file-icon[data-type="doc"] {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%) !important;
}

.file-info .file-icon[data-type="txt"] {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
}

.file-info .file-icon[data-type="img"] {
  background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%) !important;
}