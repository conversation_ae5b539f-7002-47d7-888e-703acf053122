<template>
  <div class="batch-rename-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-background">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
      </div>
      <div class="header-content">
        <div class="header-info">
          <div class="page-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="page-text">
            <h1 class="page-title">智能批量重命名</h1>
            <p class="page-subtitle">高效管理文件名称，支持多种重命名规则和实时预览</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="$router.push('/file-manager')" class="modern-btn">
            <el-icon><FolderOpened /></el-icon>
            文件管理中心
          </el-button>
          <el-button size="large" @click="previewChanges" :disabled="selectedFiles.length === 0" class="modern-btn secondary">
            <el-icon><View /></el-icon>
            预览效果
          </el-button>
        </div>
      </div>
    </div>

    <!-- 现代化统计面板 -->
    <div class="stats-panel">
      <div class="stats-container">
        <div class="stat-card primary-stat">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ totalFiles }}</div>
            <div class="stat-label">总文件数</div>
          </div>
          <div class="stat-trend">
            <el-icon><TrendCharts /></el-icon>
          </div>
        </div>

        <div class="stat-card success-stat">
          <div class="stat-icon">
            <el-icon><Select /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ selectedFiles.length }}</div>
            <div class="stat-label">已选择</div>
          </div>
          <div class="stat-trend">
            <el-icon><Check /></el-icon>
          </div>
        </div>

        <div class="stat-card warning-stat">
          <div class="stat-icon">
            <el-icon><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ previewCount }}</div>
            <div class="stat-label">预览效果</div>
          </div>
          <div class="stat-trend">
            <el-icon><Star /></el-icon>
          </div>
        </div>

        <div class="stat-card info-stat">
          <div class="stat-icon">
            <el-icon><Collection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ templates.length }}</div>
            <div class="stat-label">保存模板</div>
          </div>
          <div class="stat-trend">
            <el-icon><Operation /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="main-workspace">
      <!-- 左侧：文件选择模块 -->
      <div class="workspace-section file-selection-section">
        <div class="section-card modern-card">
          <div class="card-header">
            <div class="header-left">
              <div class="section-icon">
                <el-icon><Files /></el-icon>
              </div>
              <div class="section-info">
                <h3 class="section-title">文件选择</h3>
                <span class="section-subtitle">{{ files.length }} 个文件可用</span>
              </div>
            </div>
            <div class="header-actions">
              <el-button-group class="action-group">
                <el-button size="small" @click="selectAllFiles" :disabled="files.length === 0" class="group-btn">
                  <el-icon><Select /></el-icon>
                  全选
                </el-button>
                <el-button size="small" @click="clearSelection" :disabled="selectedFiles.length === 0" class="group-btn">
                  <el-icon><Close /></el-icon>
                  清空
                </el-button>
              </el-button-group>
              <el-button size="small" @click="loadFiles" :loading="loading" class="refresh-btn">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="card-content">
            <!-- 增强的搜索和过滤工具栏 -->
            <div class="file-toolbar" v-if="files.length > 0">
              <div class="search-section">
                <div class="search-input-wrapper">
                  <el-input
                    v-model="searchKeyword"
                    placeholder="搜索文件名..."
                    clearable
                    @input="handleSearchInput"
                    class="modern-search"
                    size="large"
                  >
                    <template #prefix>
                      <el-icon class="search-icon"><Search /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>
              
              <div class="filter-section">
                <el-select 
                  v-model="filterType" 
                  placeholder="文件类型" 
                  size="default" 
                  clearable 
                  class="type-filter"
                >
                  <el-option label="全部类型" value="" />
                  <el-option label="图片文件" value="image">
                    <div class="filter-option">
                      <el-icon><Picture /></el-icon>
                      <span>图片文件</span>
                    </div>
                  </el-option>
                  <el-option label="文档文件" value="document">
                    <div class="filter-option">
                      <el-icon><Document /></el-icon>
                      <span>文档文件</span>
                    </div>
                  </el-option>
                  <el-option label="视频文件" value="video">
                    <div class="filter-option">
                      <el-icon><VideoPlay /></el-icon>
                      <span>视频文件</span>
                    </div>
                  </el-option>
                </el-select>

                <el-dropdown @command="handleBatchCopy" :disabled="files.length === 0" class="batch-dropdown">
                  <el-button size="default" class="dropdown-trigger">
                    <el-icon><CopyDocument /></el-icon>
                    批量复制
                    <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="modern-dropdown">
                      <el-dropdown-item command="all-with-ext">
                        <el-icon><Document /></el-icon>
                        复制所有文件名（含后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="all-without-ext">
                        <el-icon><Edit /></el-icon>
                        复制所有文件名（无后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="selected-with-ext" :disabled="selectedFiles.length === 0">
                        <el-icon><Select /></el-icon>
                        复制选中文件名（含后缀）
                      </el-dropdown-item>
                      <el-dropdown-item command="selected-without-ext" :disabled="selectedFiles.length === 0">
                        <el-icon><Star /></el-icon>
                        复制选中文件名（无后缀）
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <!-- 文件列表区域 -->
            <div class="file-list-area">
              <div v-if="filteredFiles.length === 0 && !loading" class="empty-state">
                <div class="empty-illustration">
                  <div class="empty-icon">
                    <el-icon><DocumentRemove /></el-icon>
                  </div>
                  <div class="empty-content">
                    <h4>暂无文件</h4>
                    <p>请先在文件管理中心上传文件</p>
                    <el-button type="primary" @click="$router.push('/file-manager')" class="empty-action">
                      前往上传文件
                    </el-button>
                  </div>
                </div>
              </div>

              <div v-else class="file-grid">
                <!-- 现代化文件表格 -->
                <div class="modern-file-table">
                  <div class="table-header">
                    <div class="header-cell checkbox-cell">
                      <el-checkbox
                        :model-value="selectedFiles.length === paginatedFiles.length && paginatedFiles.length > 0"
                        :indeterminate="selectedFiles.length > 0 && selectedFiles.length < paginatedFiles.length"
                        @change="handleSelectAll"
                        class="header-checkbox"
                      />
                    </div>
                    <div class="header-cell name-cell">文件信息</div>
                    <div class="header-cell size-cell">大小</div>
                    <div class="header-cell index-cell">序号</div>
                    <div class="header-cell action-cell">操作</div>
                  </div>
                  
                  <div class="table-body">
                    <div
                      v-for="(file, index) in paginatedFiles"
                      :key="file.id"
                      class="table-row"
                      :class="{ 'selected': selectedFiles.includes(file) }"
                      @click="toggleFileSelection(file)"
                    >
                      <div class="table-cell checkbox-cell">
                        <el-checkbox
                          :model-value="selectedFiles.includes(file)"
                          @change="toggleFileSelection(file)"
                          @click.stop
                          class="row-checkbox"
                        />
                      </div>
                      <div class="table-cell name-cell">
                        <div class="file-info-modern">
                          <div class="file-icon-container">
                            <div class="file-icon" :style="{ backgroundColor: getFileIconColor(getFileExtension(file.original_name)) }">
                              <el-icon>
                                <component :is="getFileIcon(getFileExtension(file.original_name))" />
                              </el-icon>
                            </div>
                            <div class="file-type-badge">{{ getFileExtension(file.original_name).toUpperCase() }}</div>
                          </div>
                          <div class="file-details">
                            <div class="file-name" :title="file.original_name">{{ file.original_name }}</div>
                            <div class="file-meta">
                              <span class="file-size-text">{{ formatFileSize(file.size) }}</span>
                              <span class="file-time">{{ getRelativeTime(file.created_at) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="table-cell size-cell">
                        <div class="size-badge">{{ formatFileSize(file.size) }}</div>
                      </div>
                      <div class="table-cell index-cell">
                        <div class="index-badge">#{{ (currentPage - 1) * pageSize + index + 1 }}</div>
                      </div>
                      <div class="table-cell action-cell">
                        <el-dropdown trigger="click" @click.stop class="action-dropdown">
                          <el-button text size="small" class="action-trigger">
                            <el-icon><MoreFilled /></el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu class="action-menu">
                              <el-dropdown-item @click="copyFileName(file.original_name)">
                                <el-icon><CopyDocument /></el-icon>
                                复制完整名称
                              </el-dropdown-item>
                              <el-dropdown-item @click="copyFileNameWithoutExtension(file.original_name)">
                                <el-icon><Document /></el-icon>
                                复制无后缀名称
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 现代化分页控件 -->
                <div class="pagination-container" v-if="filteredFiles.length > pageSize">
                  <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="smartPageSizes"
                    :total="filteredFiles.length"
                    layout="total, sizes, prev, pager, next, jumper"
                    background
                    class="modern-pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：重命名规则模块 -->
      <div class="workspace-section rename-rules-section">
        <div class="section-card modern-card">
          <div class="card-header">
            <div class="header-left">
              <div class="section-icon">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="section-info">
                <h3 class="section-title">重命名规则</h3>
                <span class="section-subtitle" v-if="selectedFiles.length > 0">
                  {{ selectedFiles.length }} 个文件待处理
                </span>
                <span class="section-subtitle" v-else>请先选择文件</span>
              </div>
            </div>
            <div class="header-actions">
              <el-dropdown @command="handleTemplateCommand" class="template-dropdown">
                <el-button size="small" class="template-btn">
                  <el-icon><Star /></el-icon>
                  智能模板
                  <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="template-menu">
                    <el-dropdown-item command="date-prefix">
                      <el-icon><Calendar /></el-icon>
                      日期前缀 (YYYY-MM-DD_)
                    </el-dropdown-item>
                    <el-dropdown-item command="timestamp">
                      <el-icon><Clock /></el-icon>
                      时间戳后缀 (_HHMMSS)
                    </el-dropdown-item>
                    <el-dropdown-item command="sequence">
                      <el-icon><DataAnalysis /></el-icon>
                      序号重命名 (001, 002...)
                    </el-dropdown-item>
                    <el-dropdown-item command="lowercase">
                      <el-icon><Bottom /></el-icon>
                      转换为小写
                    </el-dropdown-item>
                    <el-dropdown-item command="uppercase">
                      <el-icon><Top /></el-icon>
                      转换为大写
                    </el-dropdown-item>
                    <el-dropdown-item divided command="import">
                      <el-icon><Upload /></el-icon>
                      导入自定义规则
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="card-content">
            <div class="rename-form">
              <!-- 快速操作标签页 -->
              <div class="quick-tabs">
                <div class="tab-list">
                  <button
                    v-for="tab in quickTabs"
                    :key="tab.value"
                    :class="['tab-item', { active: renameType === tab.value }]"
                    @click="renameType = tab.value; debouncedInputUpdate()"
                  >
                    <el-icon>
                      <component :is="tab.icon" />
                    </el-icon>
                    <span>{{ tab.label }}</span>
                  </button>
                </div>
              </div>

              <!-- 重命名类型选择 -->
              <div class="form-section">
                <label class="form-label">
                  <el-icon><Setting /></el-icon>
                  重命名方式
                </label>
                <el-select
                  v-model="renameType"
                  @change="debouncedInputUpdate"
                  class="type-selector"
                  size="large"
                  placeholder="选择重命名方式"
                >
                  <el-option 
                    v-for="option in renameOptions" 
                    :key="option.value" 
                    :label="option.label" 
                    :value="option.value"
                  >
                    <div class="select-option">
                      <el-icon>
                        <component :is="option.icon" />
                      </el-icon>
                      <div class="option-content">
                        <div class="option-title">{{ option.title }}</div>
                        <div class="option-desc">{{ option.desc }}</div>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 动态配置区域 -->
              <div class="config-area">
                <!-- 正则表达式配置 -->
                <div v-if="renameType === 'regex'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Search /></el-icon>
                      匹配模式
                    </label>
                    <el-input
                      v-model="regexPattern"
                      placeholder="例如: photo(\d+)"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                    <div class="input-hint">使用正则表达式匹配文件名模式</div>
                    <div class="example-buttons">
                      <el-button 
                        v-for="example in regexExamples" 
                        :key="example.pattern"
                        size="small" 
                        text 
                        @click="regexPattern = example.pattern; debouncedInputUpdate()"
                        class="example-btn"
                      >
                        {{ example.label }}
                      </el-button>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Edit /></el-icon>
                      替换内容
                    </label>
                    <el-input
                      v-model="regexReplacement"
                      placeholder="例如: image_$1"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    >
                      <template #prefix>
                        <el-icon><Edit /></el-icon>
                      </template>
                    </el-input>
                    <div class="input-hint">使用 $1, $2 等引用捕获组</div>
                  </div>
                </div>

                <!-- 前缀配置 -->
                <div v-else-if="renameType === 'prefix'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Plus /></el-icon>
                      前缀内容
                    </label>
                    <el-input
                      v-model="prefixText"
                      placeholder="例如: 新文件_"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    >
                      <template #prefix>
                        <el-icon><Plus /></el-icon>
                      </template>
                    </el-input>
                    <div class="input-hint">将在每个文件名前添加此内容</div>
                  </div>
                </div>

                <!-- 后缀配置 -->
                <div v-else-if="renameType === 'suffix'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Plus /></el-icon>
                      后缀内容
                    </label>
                    <el-input
                      v-model="suffixText"
                      placeholder="例如: _备份"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    >
                      <template #prefix>
                        <el-icon><Plus /></el-icon>
                      </template>
                    </el-input>
                    <div class="input-hint">将在每个文件名后添加此内容</div>
                  </div>
                </div>

                <!-- 替换配置 -->
                <div v-else-if="renameType === 'replace'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Search /></el-icon>
                      查找内容
                    </label>
                    <el-input
                      v-model="findText"
                      placeholder="要替换的文本"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Edit /></el-icon>
                      替换为
                    </label>
                    <el-input
                      v-model="replaceText"
                      placeholder="新的文本"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    />
                  </div>
                </div>

                <!-- 序号配置 -->
                <div v-else-if="renameType === 'sequence'" class="config-section">
                  <div class="form-row">
                    <div class="form-group">
                      <label class="form-label">
                        <el-icon><Document /></el-icon>
                        基础名称
                      </label>
                      <el-input
                        v-model="baseName"
                        placeholder="例如: 文件"
                        @input="debouncedInputUpdate"
                        size="large"
                        class="config-input"
                      />
                    </div>
                    <div class="form-group">
                      <label class="form-label">
                        <el-icon><Star /></el-icon>
                        起始数字
                      </label>
                      <el-input-number
                        v-model="startNumber"
                        :min="0"
                        @change="debouncedInputUpdate"
                        size="large"
                        class="config-number"
                        controls-position="right"
                      />
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Setting /></el-icon>
                      数字位数
                    </label>
                    <el-input-number
                      v-model="numberPadding"
                      :min="1"
                      :max="10"
                      @change="debouncedInputUpdate"
                      size="large"
                      class="config-number"
                      controls-position="right"
                    />
                    <div class="input-hint">设置序号的位数，如 001, 002...</div>
                  </div>
                </div>

                <!-- 映射替换配置 -->
                <div v-else-if="renameType === 'mapping'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Document /></el-icon>
                      原始数字列表
                    </label>
                    <el-input
                      v-model="mappingFrom"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入原始数字列表，每行一个或用空格分隔"
                      @input="debouncedMappingUpdate"
                      class="config-textarea"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Edit /></el-icon>
                      新数字列表
                    </label>
                    <el-input
                      v-model="mappingTo"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入新数字列表，每行一个或用空格分隔"
                      @input="debouncedMappingUpdate"
                      class="config-textarea"
                    />
                  </div>
                  <div class="mapping-status" v-if="mappingFrom || mappingTo">
                    <div class="status-item">
                      <el-icon><InfoFilled /></el-icon>
                      <span>原始数字: {{ parseMappingList(mappingFrom).length }} 个</span>
                    </div>
                    <div class="status-item">
                      <el-icon><InfoFilled /></el-icon>
                      <span>新数字: {{ parseMappingList(mappingTo).length }} 个</span>
                    </div>
                    <div class="status-item" :class="{ 'status-warning': parseMappingList(mappingFrom).length !== parseMappingList(mappingTo).length }">
                      <el-icon><Warning v-if="parseMappingList(mappingFrom).length !== parseMappingList(mappingTo).length" /><Check v-else /></el-icon>
                      <span v-if="parseMappingList(mappingFrom).length === parseMappingList(mappingTo).length">数量匹配</span>
                      <span v-else>数量不匹配，请检查</span>
                    </div>
                  </div>
                </div>

                <!-- 扩展名配置 -->
                <div v-else-if="renameType === 'extension'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><Edit /></el-icon>
                      新扩展名
                    </label>
                    <el-input
                      v-model="newExtension"
                      placeholder="例如: txt, pdf, jpg"
                      @input="debouncedInputUpdate"
                      size="large"
                      class="config-input"
                    />
                    <div class="input-hint">输入新的文件扩展名（不需要包含点号）</div>
                  </div>
                </div>

                <!-- 大小写配置 -->
                <div v-else-if="renameType === 'case'" class="config-section">
                  <div class="form-group">
                    <label class="form-label">
                      <el-icon><EditPen /></el-icon>
                      大小写类型
                    </label>
                    <el-select
                      v-model="caseType"
                      @change="debouncedInputUpdate"
                      class="type-selector"
                      size="large"
                      placeholder="选择大小写转换方式"
                    >
                      <el-option label="全部大写 (UPPERCASE)" value="upper" />
                      <el-option label="全部小写 (lowercase)" value="lower" />
                      <el-option label="首字母大写 (Title Case)" value="title" />
                    </el-select>
                  </div>
                </div>
              </div>

              <!-- 操作按钮区域 -->
              <div class="action-area">
                <div class="action-buttons">
                  <el-button
                    type="primary"
                    @click="downloadRenamedFiles"
                    :disabled="selectedFiles.length === 0 || isProcessing"
                    :loading="renaming"
                    size="large"
                    class="primary-action-btn"
                  >
                    <el-icon><Download /></el-icon>
                    <span>下载重命名文件</span>
                  </el-button>
                  
                  <el-button
                    @click="previewChanges"
                    :disabled="selectedFiles.length === 0 || isProcessing"
                    size="large"
                    class="secondary-action-btn"
                  >
                    <el-icon><View /></el-icon>
                    <span>预览效果</span>
                  </el-button>

                  <el-button
                    @click="downloadSelectedAsZip"
                    :disabled="selectedFiles.length === 0"
                    size="large"
                    class="tertiary-action-btn"
                  >
                    <el-icon><Download /></el-icon>
                    <span>ZIP下载</span>
                  </el-button>
                </div>

                <!-- 进度显示 -->
                <div v-if="showProgress" class="progress-display">
                  <div class="progress-header">
                    <h4>批量重命名进度</h4>
                    <span class="progress-stats">{{ processedCount }} / {{ totalCount }}</span>
                  </div>
                  <el-progress
                    :percentage="processingProgress"
                    :status="errorCount > 0 ? 'warning' : 'success'"
                    :stroke-width="8"
                    striped
                    striped-flow
                    class="modern-progress"
                  />
                  <div class="progress-details">
                    <span class="success-count">成功: {{ processedCount - errorCount }}</span>
                    <span v-if="errorCount > 0" class="error-count">失败: {{ errorCount }}</span>
                    <span class="remaining-count">剩余: {{ totalCount - processedCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览结果区域 -->
    <div v-if="previewResults.length > 0" class="preview-section">
      <div class="preview-card">
        <div class="preview-header">
          <div class="preview-title">
            <el-icon><View /></el-icon>
            <h3>预览效果 ({{ previewResults.length }} 个文件)</h3>
          </div>
          <el-button @click="previewResults = []" text type="primary" class="close-preview">
            <el-icon><Close /></el-icon>
            关闭预览
          </el-button>
        </div>

        <div class="preview-content">
          <div class="preview-table-header">
            <div class="preview-column">原文件名</div>
            <div class="preview-arrow-column"></div>
            <div class="preview-column">新文件名</div>
          </div>
          <div class="preview-list">
            <div
              v-for="(result, index) in previewResults"
              :key="index"
              class="preview-item"
              :class="{ 'no-changes': result.original === result.new }"
            >
              <div class="preview-original">
                <el-icon><Document /></el-icon>
                <span v-html="highlightDifferences(result.original, result.new, 'original')"></span>
              </div>
              <div class="preview-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
              <div class="preview-new" :class="{ 'has-changes': result.original !== result.new }">
                <el-icon><Edit /></el-icon>
                <span v-html="highlightDifferences(result.original, result.new, 'new')"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出重命名后的文件"
      width="500px"
      :close-on-click-modal="false"
      class="modern-dialog"
    >
      <div class="export-dialog-content">
        <div class="export-info">
          <el-icon class="export-icon"><Download /></el-icon>
          <div class="export-text">
            <h3>准备导出 {{ selectedFiles.length }} 个文件</h3>
            <p>重命名完成后，您可以选择导出方式：</p>
          </div>
        </div>
        <div class="export-options">
          <el-radio-group v-model="exportType">
            <el-radio label="zip">打包为ZIP文件下载</el-radio>
            <el-radio label="individual">逐个下载文件</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="exportFiles" :loading="exporting">
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入规则对话框 -->
    <el-dialog
      v-model="showBatchImportDialog"
      title="批量导入重命名规则"
      width="700px"
      :close-on-click-modal="false"
      class="modern-dialog"
    >
      <div class="batch-import-dialog-content">
        <div class="import-info">
          <el-icon class="import-icon"><Upload /></el-icon>
          <div class="import-text">
            <h3>批量导入重命名规则</h3>
            <p>支持导入CSV格式的重命名规则，每行格式：原文件名,新文件名</p>
          </div>
        </div>
        
        <div class="import-methods">
          <el-tabs v-model="importMethod" type="border-card">
            <el-tab-pane label="文本输入" name="text">
              <div class="text-import">
                <el-input
                  v-model="batchImportRules"
                  type="textarea"
                  :rows="10"
                  placeholder="请输入重命名规则，每行格式：原文件名,新文件名&#10;例如：&#10;photo1.jpg,vacation_001.jpg&#10;photo2.jpg,vacation_002.jpg&#10;document.pdf,report_2025.pdf"
                />
                <div class="import-tips">
                  <div class="tip-item">
                    <el-icon><InfoFilled /></el-icon>
                    <span>每行一个重命名规则，用逗号分隔原文件名和新文件名</span>
                  </div>
                  <div class="tip-item">
                    <el-icon><Warning /></el-icon>
                    <span>确保原文件名与系统中的文件完全匹配</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="文件上传" name="file">
              <div class="file-import">
                <el-upload
                  ref="importUploadRef"
                  :auto-upload="false"
                  :show-file-list="true"
                  accept=".csv,.txt"
                  :on-change="handleImportFileChange"
                  drag
                >
                  <el-icon class="upload-icon"><UploadFilled /></el-icon>
                  <div class="upload-text">
                    <div class="upload-title">拖拽CSV文件到此处</div>
                    <div class="upload-subtitle">或 <em>点击选择文件</em></div>
                  </div>
                  <template #tip>
                    <div class="upload-tips">
                      <div class="tip-item">
                        <el-icon><InfoFilled /></el-icon>
                        <span>支持CSV和TXT格式文件</span>
                      </div>
                      <div class="tip-item">
                        <el-icon><Check /></el-icon>
                        <span>文件格式：原文件名,新文件名</span>
                      </div>
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="preview-section" v-if="importPreview.length > 0">
          <h4>导入预览 ({{ importPreview.length }} 条规则)</h4>
          <div class="preview-list">
            <div
              v-for="(rule, index) in importPreview.slice(0, 5)"
              :key="index"
              class="preview-item"
            >
              <div class="preview-original">
                <el-icon><Document /></el-icon>
                <span>{{ rule.original }}</span>
              </div>
              <div class="preview-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
              <div class="preview-new">
                <el-icon><Edit /></el-icon>
                <span>{{ rule.new }}</span>
              </div>
            </div>
            <div v-if="importPreview.length > 5" class="preview-more">
              还有 {{ importPreview.length - 5 }} 条规则...
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchImportDialog = false">取消</el-button>
          <el-button @click="parseImportRules" :disabled="!batchImportRules.trim()">
            <el-icon><View /></el-icon>
            预览规则
          </el-button>
          <el-button type="primary" @click="applyImportRules" :disabled="importPreview.length === 0">
            <el-icon><Check /></el-icon>
            应用规则 ({{ importPreview.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox, ElProgress } from 'element-plus'
import {
  Refresh,
  Plus,
  DataAnalysis,
  Document,
  TrendCharts,
  Select,
  Check,
  Star,
  View,
  Operation,
  Collection,
  Lightning,
  ArrowRight,
  Close,
  DocumentRemove,
  Setting,
  Edit,
  Files,
  EditPen,
  Picture,
  VideoPlay,
  InfoFilled,
  Warning,
  Download,
  CopyDocument,
  ArrowDown,
  ArrowUp,
  Search,
  MoreFilled,
  FolderOpened,
  DocumentCopy,
  Calendar,
  Clock,
  Bottom,
  Top,
  Upload,
  UploadFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize } from '@/utils/api'
import type { FileItem } from '@/types'

const authStore = useAuthStore()

// 响应式数据
const files = ref<FileItem[]>([])
const selectedFiles = ref<FileItem[]>([])
const loading = ref(false)
const renaming = ref(false)
const templates = ref<any[]>([])
const previewResults = ref<{ original: string; new: string }[]>([])
const showExportDialog = ref(false)
const showBatchImportDialog = ref(false)
const exportType = ref('zip')
const exporting = ref(false)
const batchImportRules = ref('')
const importMethod = ref('text')
const importPreview = ref<{ original: string; new: string }[]>([])

// 新增的过滤相关状态
const filterType = ref('')

// 性能优化相关
const batchSize = ref(10)
const processingProgress = ref(0)
const showProgress = ref(false)
const isProcessing = ref(false)
const processedCount = ref(0)
const totalCount = ref(0)
const errorCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(50)

// 搜索相关
const searchKeyword = ref('')
const searchDebounceTimer = ref<number | null>(null)

// 重命名配置
const renameType = ref('regex')
const regexPattern = ref('')
const regexReplacement = ref('')
const prefixText = ref('')
const suffixText = ref('')
const findText = ref('')
const replaceText = ref('')
const baseName = ref('文件')
const startNumber = ref(1)
const numberPadding = ref(3)
const newExtension = ref('')
const caseType = ref('lower')

// 映射替换相关
const mappingFrom = ref('')
const mappingTo = ref('')

// 映射替换输入防抖
const mappingInputDebounceTimer = ref<number | null>(null)

// 快速标签页配置
const quickTabs = [
  { value: 'prefix', label: '前缀', icon: Plus },
  { value: 'suffix', label: '后缀', icon: Plus },
  { value: 'replace', label: '替换', icon: Edit },
  { value: 'sequence', label: '序号', icon: DataAnalysis },
  { value: 'regex', label: '正则', icon: Search }
]

// 重命名选项配置
const renameOptions = [
  {
    value: 'regex',
    label: '🔤 正则表达式',
    title: '正则表达式',
    desc: '使用正则匹配和替换',
    icon: Search
  },
  {
    value: 'prefix',
    label: '📝 前缀添加',
    title: '前缀添加',
    desc: '在文件名前添加内容',
    icon: Plus
  },
  {
    value: 'suffix',
    label: '📄 后缀添加',
    title: '后缀添加',
    desc: '在文件名后添加内容',
    icon: Plus
  },
  {
    value: 'replace',
    label: '🔄 替换内容',
    title: '替换内容',
    desc: '查找并替换指定文本',
    icon: Edit
  },
  {
    value: 'mapping',
    label: '🗂️ 映射替换',
    title: '映射替换',
    desc: '一对一数字映射替换',
    icon: DocumentCopy
  },
  {
    value: 'sequence',
    label: '🔢 序号重命名',
    title: '序号重命名',
    desc: '按序号批量重命名',
    icon: DataAnalysis
  },
  {
    value: 'extension',
    label: '🔧 扩展名替换',
    title: '扩展名替换',
    desc: '保留文件名但替换扩展名',
    icon: Setting
  },
  {
    value: 'case',
    label: '🔠 大小写转换',
    title: '大小写转换',
    desc: '转换文件名大小写',
    icon: EditPen
  }
]

// 正则表达式示例
const regexExamples = [
  { pattern: 'photo(\\d+)', label: 'photo(\\d+) - 匹配photo+数字' },
  { pattern: '(.+)_old', label: '(.+)_old - 匹配以_old结尾' },
  { pattern: '^IMG_(.+)', label: '^IMG_(.+) - 匹配IMG_开头' }
]

// 计算属性
const totalFiles = computed(() => files.value.length)
const previewCount = computed(() => previewResults.value.length)

// 搜索和分页计算属性
const filteredFiles = computed(() => {
  let result = files.value
  
  // 搜索过滤
  if (searchKeyword.value.trim()) {
    result = result.filter(file =>
      file.original_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  // 类型过滤
  if (filterType.value) {
    result = result.filter(file => {
      const ext = getFileExtension(file.original_name)
      switch (filterType.value) {
        case 'image':
          return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)
        case 'document':
          return ['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)
        case 'video':
          return ['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)
        default:
          return true
      }
    })
  }
  
  return result
})

// 智能分页大小计算
const smartPageSizes = computed(() => {
  const total = filteredFiles.value.length
  const baseSizes = [20, 50, 100]
  
  if (total <= 100) {
    return baseSizes
  } else if (total <= 500) {
    return [...baseSizes, 200, 500]
  } else if (total <= 1000) {
    return [...baseSizes, 200, 500, 1000, total]
  } else if (total <= 5000) {
    return [...baseSizes, 200, 500, 1000, 2000, Math.ceil(total / 2), total]
  } else {
    const quarter = Math.ceil(total / 4)
    const half = Math.ceil(total / 2)
    return [...baseSizes, 200, 500, 1000, 2000, quarter, half, total]
  }
})

const paginatedFiles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFiles.value.slice(start, end)
})

// 工具函数
const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

const getFileNameWithoutExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(0, lastDot) : filename
}

const getFileIcon = (extension: string) => {
  const ext = extension?.toLowerCase() || ''
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
    return Picture
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)) {
    return VideoPlay
  }
  return Document
}

// 获取文件图标颜色
const getFileIconColor = (extension: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#ff4d4f',
    doc: '#1890ff',
    docx: '#1890ff',
    xls: '#52c41a',
    xlsx: '#52c41a',
    ppt: '#fa8c16',
    pptx: '#fa8c16',
    txt: '#8c8c8c',
    jpg: '#722ed1',
    jpeg: '#722ed1',
    png: '#722ed1',
    gif: '#722ed1',
    bmp: '#722ed1',
    svg: '#722ed1',
    mp4: '#eb2f96',
    avi: '#eb2f96',
    mov: '#eb2f96',
    wmv: '#eb2f96',
    mp3: '#13c2c2',
    wav: '#13c2c2',
    flac: '#13c2c2',
    zip: '#faad14',
    rar: '#faad14',
    '7z': '#faad14',
    tar: '#faad14',
    gz: '#faad14'
  }
  return colorMap[extension.toLowerCase()] || '#8c8c8c'
}

// 获取相对时间
const getRelativeTime = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`

  return date.toLocaleDateString('zh-CN')
}

// 解析映射列表函数
const parseMappingList = (text: string): string[] => {
  if (!text || !text.trim()) {
    return []
  }
  
  return text.trim()
    .split(/[\n\s,]+/)
    .map(item => item.trim())
    .filter(item => item.length > 0)
}

// 文件操作
const loadFiles = async () => {
  try {
    loading.value = true
    const response = await apiMethods.files.list({ limit: 1000 })
    files.value = response.data.data.files
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
  }
}

const selectAllFiles = () => {
  selectedFiles.value = [...files.value]
  debouncedInputUpdate()
}

const clearSelection = () => {
  selectedFiles.value = []
  previewResults.value = []
}

// 切换文件选择状态
const toggleFileSelection = (file: FileItem) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file)
  }
  debouncedInputUpdate()
}

// 处理全选/取消全选
const handleSelectAll = (val: any) => {
  if (val) {
    selectedFiles.value = [...paginatedFiles.value]
  } else {
    selectedFiles.value = []
  }
  debouncedInputUpdate()
}

// 重命名逻辑
const generateNewName = (originalName: string, index: number): string => {
  const extension = getFileExtension(originalName)
  const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName

  switch (renameType.value) {
    case 'regex':
      if (regexPattern.value && regexReplacement.value) {
        try {
          const regex = new RegExp(regexPattern.value, 'g')
          const newName = nameWithoutExt.replace(regex, regexReplacement.value)
          return extension ? `${newName}.${extension}` : newName
        } catch (error) {
          return originalName
        }
      }
      return originalName

    case 'prefix':
      return extension ? `${prefixText.value}${nameWithoutExt}.${extension}` : `${prefixText.value}${nameWithoutExt}`

    case 'suffix':
      return extension ? `${nameWithoutExt}${suffixText.value}.${extension}` : `${nameWithoutExt}${suffixText.value}`

    case 'replace':
      if (findText.value) {
        const newName = nameWithoutExt.replace(new RegExp(findText.value, 'g'), replaceText.value)
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'mapping':
      if (mappingFrom.value && mappingTo.value) {
        const fromList = parseMappingList(mappingFrom.value)
        const toList = parseMappingList(mappingTo.value)
        
        if (fromList.length !== toList.length) {
          return originalName
        }
        
        const mappingMap = new Map<string, string>()
        fromList.forEach((from, index) => {
          mappingMap.set(from, toList[index])
        })
        
        let newName = nameWithoutExt
        for (const [from, to] of mappingMap) {
          if (newName.includes(from)) {
            newName = newName.replace(new RegExp(from, 'g'), to)
            break
          }
        }
        
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'sequence':
      const number = (startNumber.value + index).toString().padStart(numberPadding.value, '0')
      return extension ? `${baseName.value}${number}.${extension}` : `${baseName.value}${number}`

    case 'extension':
      if (newExtension.value.trim()) {
        const cleanExtension = newExtension.value.trim().replace(/^\.+/, '')
        return cleanExtension ? `${nameWithoutExt}.${cleanExtension}` : nameWithoutExt
      }
      return originalName

    case 'case':
      switch (caseType.value) {
        case 'upper':
          return originalName.toUpperCase()
        case 'lower':
          return originalName.toLowerCase()
        case 'title':
          return originalName.toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
        default:
          return originalName
      }

    default:
      return originalName
  }
}

// 防抖优化的预览更新
const updatePreviewDebounceTimer = ref<number | null>(null)

const updatePreview = () => {
  if (updatePreviewDebounceTimer.value) {
    clearTimeout(updatePreviewDebounceTimer.value)
  }
  
  updatePreviewDebounceTimer.value = window.setTimeout(() => {
    if (selectedFiles.value.length === 0) {
      previewResults.value = []
      return
    }

    const maxPreviewCount = selectedFiles.value.length > 200 ? 50 : 100
    const filesToPreview = selectedFiles.value.slice(0, maxPreviewCount)
    
    requestAnimationFrame(() => {
      previewResults.value = filesToPreview.map((file, index) => ({
        original: file.original_name,
        new: generateNewName(file.original_name, index)
      }))
    })

    if (selectedFiles.value.length > maxPreviewCount) {
      ElMessage.info(`预览显示前${maxPreviewCount}个文件，共选中${selectedFiles.value.length}个文件`)
    }
  }, 300)
}

// 防抖搜索
const debouncedSearch = () => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  searchDebounceTimer.value = window.setTimeout(() => {
    handleSearch()
  }, 300)
}

// 统一的输入防抖处理
const debouncedInputUpdate = () => {
  if (updatePreviewDebounceTimer.value) {
    clearTimeout(updatePreviewDebounceTimer.value)
  }
  updatePreviewDebounceTimer.value = window.setTimeout(() => {
    updatePreview()
  }, 300)
}

// 映射替换输入防抖处理
const debouncedMappingUpdate = () => {
  if (mappingInputDebounceTimer.value) {
    clearTimeout(mappingInputDebounceTimer.value)
  }
  mappingInputDebounceTimer.value = window.setTimeout(() => {
    updatePreview()
  }, 500)
}

const previewChanges = () => {
  debouncedInputUpdate()
  if (previewResults.value.length === 0) {
    ElMessage.warning('请先选择文件并配置重命名规则')
  }
}

// 搜索功能
const handleSearch = () => {
  currentPage.value = 1
}

// 优化的搜索输入处理
const handleSearchInput = () => {
  debouncedSearch()
}

// 分页处理函数
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
}

// 下载重命名后的文件
const downloadRenamedFiles = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要重命名的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要下载重命名后的 ${selectedFiles.value.length} 个文件吗？`,
      '确认下载',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    renaming.value = true
    isProcessing.value = true

    try {
      const renameData = {
        file_ids: selectedFiles.value.map(file => file.id),
        type: renameType.value,
        pattern: regexPattern.value,
        replacement: regexReplacement.value,
        prefix: prefixText.value,
        suffix: suffixText.value,
        find_text: findText.value,
        replace_text: replaceText.value,
        base_name: baseName.value,
        start_number: startNumber.value,
        number_padding: numberPadding.value,
        new_extension: newExtension.value,
        mapping_from: mappingFrom.value,
        mapping_to: mappingTo.value
      }

      const response = await fetch('/api/v1/rename/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(renameData)
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          previewResults.value = result.data.previews.map((preview: any) => ({
            original: preview.original_name,
            new: preview.new_name
          }))
          
          const fileNameMapping: Record<number, string> = {}
          result.data.previews.forEach((preview: any, index: number) => {
            const fileId = selectedFiles.value[index]?.id
            if (fileId && preview.new_name) {
              fileNameMapping[fileId] = preview.new_name
            }
          })
          
          await downloadSelectedAsZipWithRename(fileNameMapping)
          
          ElMessage.success(`成功生成 ${result.data.total_count} 个重命名文件的下载`)
        } else {
          ElMessage.error(result.message || '生成重命名文件失败')
        }
      } else {
        ElMessage.error('请求失败，请重试')
      }
    } catch (error) {
      console.error('下载重命名文件失败:', error)
      ElMessage.error('下载重命名文件失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量重命名失败:', error)
      ElMessage.error('批量重命名失败')
    }
  } finally {
    
    renaming.value = false
    isProcessing.value = false
  }
}

// 处理模板命令
const handleTemplateCommand = (command: string) => {
  switch (command) {
    case 'date-prefix':
      renameType.value = 'prefix'
      prefixText.value = new Date().toISOString().split('T')[0] + '_'
      debouncedInputUpdate()
      ElMessage.success('已应用日期前缀模板')
      break
    case 'timestamp':
      renameType.value = 'suffix'
      const now = new Date()
      suffixText.value = '_' + now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
      debouncedInputUpdate()
      ElMessage.success('已应用时间戳后缀模板')
      break
    case 'sequence':
      renameType.value = 'sequence'
      baseName.value = '文件_'
      startNumber.value = 1
      numberPadding.value = 3
      debouncedInputUpdate()
      ElMessage.success('已应用序号重命名模板')
      break
    case 'lowercase':
      renameType.value = 'case'
      caseType.value = 'lower'
      debouncedInputUpdate()
      ElMessage.success('已应用小写转换模板')
      break
    case 'uppercase':
      renameType.value = 'case'
      caseType.value = 'upper'
      debouncedInputUpdate()
      ElMessage.success('已应用大写转换模板')
      break
    case 'import':
      showBatchImportDialog.value = true
      break
  }
}

// 导出相关函数
const exportFiles = async () => {
  try {
    exporting.value = true
    
    if (exportType.value === 'individual') {
      for (const file of selectedFiles.value) {
        try {
          const response = await apiMethods.files.download(file.id)
          const blob = new Blob([response.data])
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = file.original_name
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        } catch (error) {
          console.error(`下载文件 ${file.original_name} 失败:`, error)
        }
      }
      ElMessage.success('文件导出完成')
    } else {
      try {
        const fileIds = selectedFiles.value.map(file => file.id)
        const response = await apiMethods.files.downloadZip(fileIds)
        
        const blob = new Blob([response.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `批量重命名文件_${new Date().toISOString().split('T')[0]}.zip`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('ZIP文件下载完成')
      } catch (error) {
        console.error('ZIP下载失败:', error)
        ElMessage.error('ZIP下载失败，请重试')
      }
    }
    
    showExportDialog.value = false
    clearSelection()
  } catch (error) {
    console.error('导出文件失败:', error)
    ElMessage.error('导出文件失败')
  } finally {
    exporting.value = false
  }
}

// ZIP下载相关函数
const downloadSelectedAsZip = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件')
    return
  }

  try {
    exporting.value = true
    const fileIds = selectedFiles.value.map(file => file.id)
    const response = await apiMethods.files.downloadZip(fileIds)
    
    const blob = new Blob([response.data], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `选中文件_${new Date().toISOString().split('T')[0]}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('ZIP文件下载完成')
  } catch (error) {
    console.error('ZIP下载失败:', error)
    ElMessage.error('ZIP下载失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 使用重命名后的文件名下载ZIP
const downloadSelectedAsZipWithRename = async (fileNameMapping: Record<number, string>) => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件')
    return
  }

  try {
    exporting.value = true
    const fileIds = selectedFiles.value.map(file => file.id)
    
    const response = await fetch('/api/v1/files/download-zip', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        file_ids: fileIds,
        file_names: fileNameMapping
      })
    })

    if (!response.ok) {
      throw new Error('下载请求失败')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `重命名文件_${new Date().toISOString().split('T')[0]}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('重命名文件ZIP下载完成')
  } catch (error) {
    console.error('重命名ZIP下载失败:', error)
    ElMessage.error('重命名ZIP下载失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 批量导入相关函数
const handleImportFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    batchImportRules.value = content
    parseImportRules()
  }
  reader.readAsText(file.raw)
}

const parseImportRules = () => {
  if (!batchImportRules.value.trim()) {
    importPreview.value = []
    return
  }

  const lines = batchImportRules.value.trim().split('\n')
  const rules: { original: string; new: string }[] = []

  for (const line of lines) {
    const trimmedLine = line.trim()
    if (!trimmedLine) continue

    const parts = trimmedLine.split(',')
    if (parts.length >= 2) {
      const original = parts[0].trim()
      const newName = parts.slice(1).join(',').trim()
      if (original && newName) {
        rules.push({ original, new: newName })
      }
    }
  }

  importPreview.value = rules
  if (rules.length > 0) {
    ElMessage.success(`成功解析 ${rules.length} 条重命名规则`)
  } else {
    ElMessage.warning('未找到有效的重命名规则')
  }
}

const applyImportRules = async () => {
  if (importPreview.value.length === 0) {
    ElMessage.warning('没有可应用的重命名规则')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要应用 ${importPreview.value.length} 条重命名规则吗？`,
      '确认批量重命名',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    renaming.value = true
    let successCount = 0
    let errorCount = 0

    for (const rule of importPreview.value) {
      const file = files.value.find(f => f.original_name === rule.original)
      if (file) {
        try {
          await apiMethods.files.update(file.id, { new_name: rule.new })
          successCount++
        } catch (error) {
          console.error(`重命名文件 ${rule.original} 失败:`, error)
          errorCount++
        }
      } else {
        errorCount++
        console.warn(`未找到文件: ${rule.original}`)
      }
    }

    if (successCount > 0) {
      ElMessage.success(`成功重命名 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个失败` : ''}`)
      await loadFiles()
      showBatchImportDialog.value = false
      batchImportRules.value = ''
      importPreview.value = []
    } else {
      ElMessage.error('没有文件被重命名，请检查文件名是否匹配')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量重命名失败:', error)
      ElMessage.error('批量重命名失败')
    }
  } finally {
    renaming.value = false
  }
}

// 复制功能
const copyFileName = async (filename: string) => {
  try {
    await navigator.clipboard.writeText(filename)
    ElMessage.success('文件名已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

const copyFileNameWithoutExtension = async (filename: string) => {
  try {
    const nameWithoutExt = getFileNameWithoutExtension(filename)
    await navigator.clipboard.writeText(nameWithoutExt)
    ElMessage.success('文件名（无后缀）已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 批量复制功能
const handleBatchCopy = async (command: string) => {
  try {
    let fileNames: string[] = []
    
    switch (command) {
      case 'all-with-ext':
        fileNames = files.value.map(file => file.original_name)
        break
      case 'all-without-ext':
        fileNames = files.value.map(file => getFileNameWithoutExtension(file.original_name))
        break
      case 'selected-with-ext':
        fileNames = selectedFiles.value.map(file => file.original_name)
        break
      case 'selected-without-ext':
        fileNames = selectedFiles.value.map(file => getFileNameWithoutExtension(file.original_name))
        break
      default:
        return
    }
    
    if (fileNames.length === 0) {
      ElMessage.warning('没有文件可复制')
      return
    }
    
    const textToCopy = fileNames.join('\n')
    await navigator.clipboard.writeText(textToCopy)
    
    const typeText = command.includes('selected') ? '选中' : '所有'
    const extText = command.includes('without') ? '（无后缀）' : '（含后缀）'
    ElMessage.success(`已复制 ${fileNames.length} 个${typeText}文件名${extText}到剪贴板`)
  } catch (error) {
    console.error('批量复制失败:', error)
    ElMessage.error('批量复制失败，请重试')
  }
}

// 高亮显示文件名差异
const highlightDifferences = (original: string, newName: string, type: 'original' | 'new') => {
  if (original === newName) {
    return original
  }

  const text = type === 'original' ? original : newName
  const other = type === 'original' ? newName : original

  if (text.length === 0 || other.length === 0) {
    return `<span class="diff-highlight ${type === 'new' ? 'diff-added' : 'diff-removed'}">${text}</span>`
  }

  let prefixEnd = 0
  let suffixStart = text.length

  while (prefixEnd < text.length && prefixEnd < other.length &&
         text[prefixEnd] === other[prefixEnd]) {
    prefixEnd++
  }

  let textSuffixPos = text.length - 1
  let otherSuffixPos = other.length - 1
  while (textSuffixPos >= prefixEnd && otherSuffixPos >= prefixEnd &&
         text[textSuffixPos] === other[otherSuffixPos]) {
    textSuffixPos--
    otherSuffixPos--
  }
  suffixStart = textSuffixPos + 1

  const prefix = text.substring(0, prefixEnd)
  const middle = text.substring(prefixEnd, suffixStart)
  const suffix = text.substring(suffixStart)

  if (middle.length === 0) {
    return text
  }

  const highlightClass = type === 'new' ? 'diff-added' : 'diff-removed'
  return `${prefix}<span class="diff-highlight ${highlightClass}">${middle}</span>${suffix}`
}

onMounted(() => {
  loadFiles()
})
</script>

<style scoped>
/* 现代化批量重命名页面样式 */
.batch-rename-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面头部样式 */
.page-header {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  filter: blur(40px);
}

.orb-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
}

.orb-2 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
  background: linear-gradient(135deg, #10b981, #06b6d4);
}

.orb-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.header-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.modern-btn {
  height: 48px;
  padding: 0 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.modern-btn.secondary {
  background: white;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.modern-btn.secondary:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 统计面板样式 */
.stats-panel {
  margin-bottom: 32px;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::before {
  width: 8px;
}

.stat-card.primary-stat::before {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-card.success-stat::before {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card.warning-stat::before {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card.info-stat::before {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.primary-stat .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.success-stat .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.warning-stat .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.info-stat .stat-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.stat-trend {
  color: #94a3b8;
  font-size: 16px;
}

/* 主工作区域样式 */
.main-workspace {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  align-items: start;
}

.workspace-section {
  display: flex;
  flex-direction: column;
}

.section-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.section-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.card-header {
  padding: 24px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 4px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-group .group-btn {
  height: 36px;
  padding: 0 16px;
  font-size: 13px;
  border-radius: 8px;
}

.refresh-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  padding: 0;
}

.card-content {
  padding: 24px;
}

/* 文件工具栏样式 */
.file-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding: 20px;
  margin: -24px -24px 24px -24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-bottom: 1px solid #e2e8f0;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.modern-search :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.modern-search :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-search :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 文件表格样式 */
.modern-file-table {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background: white;
}

.table-header {
  display: grid;
  grid-template-columns: 50px 1fr 100px 80px 80px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  font-size: 13px;
  color: #475569;
}

.table-body {
  max-height: 500px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 1fr 100px 80px 80px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
  cursor: pointer;
}

.table-row:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
}

.table-row.selected {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-left: 3px solid #3b82f6;
}

.header-cell,
.table-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.checkbox-cell {
  justify-content: center;
}

.name-cell {
  min-width: 0;
}

.size-cell,
.index-cell,
.action-cell {
  justify-content: center;
}

/* 文件信息样式 */
.file-info-modern {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.file-icon-container {
  position: relative;
  flex-shrink: 0;
}

.file-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.file-type-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #3b82f6;
  color: white;
  font-size: 9px;
  font-weight: 700;
  padding: 2px 4px;
  border-radius: 4px;
  text-transform: uppercase;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.size-badge,
.index-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.action-trigger {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: all 0.2s ease;
}

.action-trigger:hover {
  background: #3b82f6;
  color: white;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  margin-top: 16px;
  border-top: 1px solid #f1f5f9;
}

/* 重命名表单样式 */
.rename-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.quick-tabs {
  margin-bottom: 20px;
}

.tab-list {
  display: flex;
  background: #f1f5f9;
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.tab-item.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-label .el-icon {
  color: #3b82f6;
  font-size: 16px;
}

.type-selector {
  width: 100%;
}

.type-selector :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.type-selector :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.type-selector :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.select-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 2px;
}

.option-desc {
  font-size: 12px;
  color: #64748b;
}

/* 配置区域样式 */
.config-area {
  margin-top: 20px;
}

.config-section {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.config-input,
.config-textarea,
.config-number {
  width: 100%;
}

.config-input :deep(.el-input__wrapper),
.config-textarea :deep(.el-textarea__inner),
.config-number :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.config-input :deep(.el-input__wrapper:hover),
.config-textarea:hover :deep(.el-textarea__inner),
.config-number :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.config-input :deep(.el-input__wrapper.is-focus),
.config-textarea:focus-within :deep(.el-textarea__inner),
.config-number :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.input-hint {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  line-height: 1.4;
}

.example-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.example-btn {
  font-size: 12px;
  padding: 6px 12px;
  height: auto;
  border-radius: 8px;
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  transition: all 0.2s ease;
}

.example-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

/* 映射状态样式 */
.mapping-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #1e293b;
}

.status-item .el-icon {
  font-size: 16px;
  color: #3b82f6;
}

.status-item.status-warning {
  color: #f59e0b;
}

.status-item.status-warning .el-icon {
  color: #f59e0b;
}

/* 操作按钮区域样式 */
.action-area {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.action-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.action-buttons {
  display: flex;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.primary-action-btn,
.secondary-action-btn,
.tertiary-action-btn {
  flex: 1;
  height: 52px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.primary-action-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.secondary-action-btn {
  background: white;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.secondary-action-btn:hover {
  background: #f9fafb;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.tertiary-action-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.tertiary-action-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

/* 进度显示样式 */
.progress-display {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.progress-stats {
  font-size: 14px;
  font-weight: 500;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 13px;
  font-weight: 500;
}

.success-count {
  color: #10b981;
}

.error-count {
  color: #ef4444;
}

.remaining-count {
  color: #64748b;
}

/* 预览区域样式 */
.preview-section {
  margin-top: 32px;
}

.preview-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.preview-content {
  padding: 0;
}

.preview-table-header {
  display: grid;
  grid-template-columns: 1fr 60px 1fr;
  gap: 16px;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 14px;
  color: #475569;
}

.preview-column {
  text-align: center;
}

.preview-list {
  max-height: 400px;
  overflow-y: auto;
}

.preview-item {
  display: grid;
  grid-template-columns: 1fr 60px 1fr;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.preview-item:hover {
  background: #f8fafc;
}

.preview-item.no-changes {
  opacity: 0.6;
}

.preview-original,
.preview-new {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  min-width: 0;
}

.preview-original {
  color: #64748b;
}

.preview-new {
  color: #1e293b;
  font-weight: 500;
}

.preview-new.has-changes {
  color: #3b82f6;
}

.preview-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 18px;
}

/* 差异高亮样式 */
.diff-highlight {
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

.diff-added {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.diff-removed {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* 对话框样式 */
.modern-dialog :deep(.el-dialog) {
  border-radius: 20px;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.export-dialog-content,
.batch-import-dialog-content {
  padding: 24px;
}

.export-info,
.import-info {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.export-icon,
.import-icon {
  font-size: 40px;
  color: #3b82f6;
  margin-right: 16px;
}

.export-text,
.import-text {
  flex: 1;
}

.export-text h3,
.import-text h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.export-text p,
.import-text p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.export-options {
  padding: 0 20px;
}

.export-options :deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-options :deep(.el-radio) {
  margin: 0;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.export-options :deep(.el-radio:hover) {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.export-options :deep(.el-radio.is-checked) {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.import-methods {
  margin-bottom: 24px;
}

.text-import,
.file-import {
  padding: 16px 0;
}

.import-tips,
.upload-tips {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #64748b;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.upload-icon {
  font-size: 48px;
  color: #3b82f6;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.upload-subtitle {
  font-size: 14px;
  color: #64748b;
}

.upload-subtitle em {
  color: #3b82f6;
  font-style: normal;
  font-weight: 500;
}

.preview-more {
  text-align: center;
  padding: 12px;
  color: #64748b;
  font-size: 13px;
  background: #f8fafc;
  border-radius: 6px;
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-workspace {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .batch-rename-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .file-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .filter-section {
    justify-content: flex-start;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 40px 1fr 80px 60px 60px;
  }
  
  .header-cell,
  .table-cell {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .preview-table-header,
  .preview-item {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }
  
  .preview-arrow {
    display: none;
  }
}

@media (max-width: 480px) {
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .tab-list {
    flex-wrap: wrap;
  }
  
  .tab-item {
    min-width: 0;
    flex: 1 1 auto;
  }
  
  .example-buttons {
    flex-direction: column;
  }
  
  .example-btn {
    width: 100%;
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.empty-content p {
  margin: 0 0 20px 0;
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
}

.empty-action {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
}

/* 自定义滚动条 */
.table-body::-webkit-scrollbar,
.preview-list::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track,
.preview-list::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb,
.preview-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.table-body::-webkit-scrollbar-thumb:hover,
.preview-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 优化的按钮悬停效果 */
.modern-btn::before,
.primary-action-btn::before,
.secondary-action-btn::before,
.tertiary-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before,
.primary-action-btn:hover::before,
.secondary-action-btn:hover::before,
.tertiary-action-btn:hover::before {
  left: 100%;
}
</style>