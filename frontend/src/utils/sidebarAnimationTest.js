/**
 * 侧边栏动画测试工具
 * 用于验证侧边栏动画修复效果
 */

export class SidebarAnimationTester {
  constructor() {
    this.testResults = []
  }

  /**
   * 测试动画时间一致性
   */
  testAnimationTiming() {
    const sidebar = document.querySelector('.sidebar')
    if (!sidebar) {
      return { success: false, message: '未找到侧边栏元素' }
    }

    const computedStyle = window.getComputedStyle(sidebar)
    const transition = computedStyle.transition

    // 检查是否包含统一的0.2s时间
    const hasUnifiedTiming = transition.includes('0.2s')
    
    return {
      success: hasUnifiedTiming,
      message: hasUnifiedTiming ? '动画时间统一为0.2s' : '动画时间不统一',
      details: { transition }
    }
  }

  /**
   * 测试状态管理
   */
  testStateManagement() {
    const sidebar = document.querySelector('.sidebar')
    if (!sidebar) {
      return { success: false, message: '未找到侧边栏元素' }
    }

    // 检查是否有正确的状态类
    const hasCollapsedClass = sidebar.classList.contains('collapsed')
    const hasNavigatingClass = sidebar.classList.contains('is-navigating')
    const hasTogglingClass = sidebar.classList.contains('sidebar-toggling')

    return {
      success: true,
      message: '状态管理正常',
      details: {
        collapsed: hasCollapsedClass,
        navigating: hasNavigatingClass,
        toggling: hasTogglingClass
      }
    }
  }

  /**
   * 测试fade动画
   */
  testFadeAnimation() {
    const fadeElements = document.querySelectorAll('.fade-enter-active, .fade-leave-active')
    
    if (fadeElements.length === 0) {
      return { success: true, message: '当前没有fade动画元素' }
    }

    let allCorrect = true
    const details = []

    fadeElements.forEach((element, index) => {
      const computedStyle = window.getComputedStyle(element)
      const transition = computedStyle.transition
      
      const hasCorrectTiming = transition.includes('0.2s')
      if (!hasCorrectTiming) {
        allCorrect = false
      }

      details.push({
        element: index,
        transition,
        correct: hasCorrectTiming
      })
    })

    return {
      success: allCorrect,
      message: allCorrect ? 'Fade动画时间正确' : '部分fade动画时间不正确',
      details
    }
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🔍 开始侧边栏动画测试...')
    
    const tests = [
      { name: '动画时间一致性', test: () => this.testAnimationTiming() },
      { name: '状态管理', test: () => this.testStateManagement() },
      { name: 'Fade动画', test: () => this.testFadeAnimation() }
    ]

    this.testResults = []

    tests.forEach(({ name, test }) => {
      try {
        const result = test()
        this.testResults.push({ name, ...result })
        
        const status = result.success ? '✅' : '❌'
        console.log(`${status} ${name}: ${result.message}`)
        
        if (result.details) {
          console.log('   详情:', result.details)
        }
      } catch (error) {
        this.testResults.push({ 
          name, 
          success: false, 
          message: `测试失败: ${error.message}` 
        })
        console.log(`❌ ${name}: 测试失败 - ${error.message}`)
      }
    })

    const passedTests = this.testResults.filter(r => r.success).length
    const totalTests = this.testResults.length

    console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！侧边栏动画修复成功。')
    } else {
      console.log('⚠️  部分测试未通过，可能需要进一步调整。')
    }

    return this.testResults
  }

  /**
   * 模拟侧边栏切换测试
   */
  simulateToggle() {
    const toggleButton = document.querySelector('.collapse-btn-expanded, .collapse-btn-collapsed')
    
    if (!toggleButton) {
      console.log('❌ 未找到切换按钮')
      return false
    }

    console.log('🔄 模拟侧边栏切换...')
    
    // 记录切换前状态
    const sidebar = document.querySelector('.sidebar')
    const wasCollapsed = sidebar.classList.contains('collapsed')
    
    // 触发切换
    toggleButton.click()
    
    // 检查状态变化
    setTimeout(() => {
      const isCollapsed = sidebar.classList.contains('collapsed')
      const stateChanged = wasCollapsed !== isCollapsed
      
      const status = stateChanged ? '✅' : '❌'
      console.log(`${status} 切换测试: ${stateChanged ? '状态正常切换' : '状态未改变'}`)
      
      return stateChanged
    }, 100)
  }
}

// 创建全局测试实例
window.sidebarTester = new SidebarAnimationTester()

// 提供便捷的测试方法
window.testSidebar = () => window.sidebarTester.runAllTests()
window.testSidebarToggle = () => window.sidebarTester.simulateToggle()

console.log('🛠️  侧边栏动画测试工具已加载')
console.log('💡 使用 testSidebar() 运行测试')
console.log('💡 使用 testSidebarToggle() 测试切换功能')
