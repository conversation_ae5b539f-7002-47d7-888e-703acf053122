import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { api } from '@/utils/api'
import type { User, LoginRequest, RegisterRequest, UserManagement, UpdateUserRoleRequest } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const userRole = computed(() => user.value?.role || 'user')

  // 设置认证信息
  const setAuth = (userData: User, authToken: string) => {
    user.value = userData
    token.value = authToken
    localStorage.setItem('token', authToken)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 验证token格式和有效性
  const validateToken = (tokenString: string): boolean => {
    if (!tokenString) return false
    
    // 检查Bearer格式
    if (!tokenString.startsWith('Bearer ') && !tokenString.includes('.')) {
      return false
    }
    
    try {
      // 简单的JWT格式检查（三个部分用.分隔）
      const tokenParts = tokenString.replace('Bearer ', '').split('.')
      if (tokenParts.length !== 3) {
        return false
      }
      
      // 解析payload检查过期时间
      const payload = JSON.parse(atob(tokenParts[1]))
      if (payload.exp && payload.exp * 1000 < Date.now()) {
        return false
      }
      
      return true
    } catch (error) {
      console.warn('Token validation failed:', error)
      return false
    }
  }

  // 验证用户信息完整性
  const validateUserData = (userData: any): userData is User => {
    return userData &&
           typeof userData.id === 'number' &&
           typeof userData.username === 'string' &&
           typeof userData.email === 'string' &&
           typeof userData.role === 'string'
  }

  // 初始化用户信息
  const initAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (!savedToken || !savedUser) {
      clearAuth()
      return
    }

    try {
      // 验证token格式
      if (!validateToken(savedToken)) {
        console.warn('Invalid token format, clearing auth')
        clearAuth()
        return
      }

      // 验证用户数据
      const userData = JSON.parse(savedUser)
      if (!validateUserData(userData)) {
        console.warn('Invalid user data, clearing auth')
        clearAuth()
        return
      }

      // 设置基本信息
      token.value = savedToken
      user.value = userData

      // 验证token是否仍然有效（通过API调用）
      try {
        await api.get('/users/profile')
        // 如果API调用成功，说明token有效
      } catch (error: any) {
        // 如果API调用失败，检查是否是认证错误
        if (error.response?.status === 401) {
          console.warn('Token validation failed, clearing auth')
          clearAuth()
        }
      }
    } catch (error) {
      console.error('Auth initialization failed:', error)
      clearAuth()
    }
  }

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await api.post('/auth/login', credentials)
      const { user: userData, token: authToken } = response.data.data
      
      // 验证返回的数据
      if (!validateUserData(userData) || !authToken) {
        return {
          success: false,
          message: '服务器返回的数据格式错误'
        }
      }
      
      setAuth(userData, authToken)
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '登录失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    try {
      const response = await api.post('/auth/register', userData)
      const { user: newUser, token: authToken } = response.data.data
      
      // 验证返回的数据
      if (!validateUserData(newUser) || !authToken) {
        return {
          success: false,
          message: '服务器返回的数据格式错误'
        }
      }
      
      setAuth(newUser, authToken)
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '注册失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    clearAuth()
  }

  // 获取用户资料
  const fetchProfile = async () => {
    try {
      const response = await api.get('/users/profile')
      const userData = response.data.data
      
      // 验证返回的用户数据
      if (!validateUserData(userData)) {
        console.error('Invalid user data received from profile API')
        clearAuth()
        return
      }
      
      user.value = userData
      localStorage.setItem('user', JSON.stringify(userData))
    } catch (error: any) {
      console.error('获取用户资料失败:', error)
      
      // 如果是认证错误，清除认证信息
      if (error.response?.status === 401) {
        clearAuth()
      }
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const response = await api.put('/users/profile', profileData)
      const userData = response.data.data
      
      // 验证返回的用户数据
      if (!validateUserData(userData)) {
        return {
          success: false,
          message: '服务器返回的数据格式错误'
        }
      }
      
      user.value = userData
      localStorage.setItem('user', JSON.stringify(userData))
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '更新失败'
      }
    }
  }

  // 修改密码
  const changePassword = async (passwordData: { old_password: string; new_password: string }) => {
    try {
      await api.post('/users/change-password', passwordData)
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '修改密码失败'
      }
    }
  }

  // 获取所有用户（管理员功能）
  const getAllUsers = async (): Promise<{ success: boolean; data?: UserManagement[]; message?: string }> => {
    if (!isAdmin.value) {
      return { success: false, message: '权限不足' }
    }
    
    try {
      const response = await api.get('/users/')
      return { success: true, data: response.data.data }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取用户列表失败'
      }
    }
  }

  // 更新用户角色（管理员功能）
  const updateUserRole = async (roleData: UpdateUserRoleRequest): Promise<{ success: boolean; message?: string }> => {
    if (!isAdmin.value) {
      return { success: false, message: '权限不足' }
    }
    
    try {
      await api.put('/users/role', roleData)
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '更新用户角色失败'
      }
    }
  }

  // 删除用户（管理员功能）
  const deleteUser = async (userId: number): Promise<{ success: boolean; message?: string }> => {
    if (!isAdmin.value) {
      return { success: false, message: '权限不足' }
    }
    
    try {
      await api.delete(`/users/${userId}`)
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '删除用户失败'
      }
    }
  }

  // 检查是否有管理员权限
  const hasAdminPermission = (): boolean => {
    return isAdmin.value
  }

  // 检查是否可以管理指定用户
  const canManageUser = (targetUserId: number): boolean => {
    return isAdmin.value && user.value?.id !== targetUserId
  }

  return {
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    isAuthenticated,
    isAdmin,
    userRole,
    setAuth,
    clearAuth,
    initAuth,
    login,
    register,
    logout,
    fetchProfile,
    updateProfile,
    changePassword,
    getAllUsers,
    updateUserRole,
    deleteUser,
    hasAdminPermission,
    canManageUser,
    validateToken,
    validateUserData
  }
})