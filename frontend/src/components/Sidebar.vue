<template>
  <div class="sidebar" :class="{
    collapsed,
    'is-navigating': isNavigating,
    'sidebar-toggling': isToggling
  }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo">
        <div class="logo-icon">
          <el-icon><Files /></el-icon>
        </div>
        <transition name="fade">
          <span v-show="!collapsed" class="logo-text">文件管理工具</span>
        </transition>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav" role="navigation" aria-label="主导航">
      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">主要功能</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in mainMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: $route.path === item.path,
              'no-transition': isNavigating
            }"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
            <transition name="fade">
              <div v-show="!collapsed && item.badge" class="nav-badge">
                {{ item.badge }}
              </div>
            </transition>
          </router-link>
        </div>
      </div>

      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">系统管理</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in systemMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: $route.path === item.path,
              'no-transition': isNavigating
            }"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 展开状态的按钮 -->
      <div
        v-if="!collapsed"
        class="collapse-btn-expanded"
        @click="handleToggle"
        role="button"
        aria-label="收起侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Fold />
        </el-icon>
      </div>
      <!-- 收缩状态的按钮 -->
      <div
        v-else
        class="collapse-btn-collapsed"
        @click="handleToggle"
        role="button"
        aria-label="展开侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Expand />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Files,
  DataBoard,
  Upload,
  Edit,
  List,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()
const emit = defineEmits<{
  toggle: []
}>()

const route = useRoute()
const router = useRouter()

// 简化的状态管理
const isToggling = ref(false)
const isNavigating = ref(false)

const handleToggle = () => {
  if (isToggling.value) return

  isToggling.value = true
  emit('toggle')

  // 统一动画时间为250ms，留出缓冲
  setTimeout(() => {
    isToggling.value = false
  }, 250)
}

// 优化路由切换处理
let navigationTimer: NodeJS.Timeout | null = null
watch(() => route.path, () => {
  // 只在切换动画进行时才禁用导航动画
  if (!isToggling.value) {
    isNavigating.value = true

    // 清除之前的定时器
    if (navigationTimer) {
      clearTimeout(navigationTimer)
    }

    // 快速恢复，避免影响正常交互
    navigationTimer = setTimeout(() => {
      isNavigating.value = false
    }, 100)
  }
})

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (navigationTimer) {
    clearTimeout(navigationTimer)
  }
})

// 主要功能菜单项
const mainMenuItems = computed(() => [
  {
    path: '/dashboard',
    title: '仪表板',
    icon: 'DataBoard',
    badge: null
  },
  {
    path: '/files',
    title: '文件管理',
    icon: 'Upload',
    badge: null
  },
  {
    path: '/rename',
    title: '批量重命名',
    icon: 'Edit',
    badge: null
  }
])

// 系统管理菜单项
const systemMenuItems = computed(() => [
  {
    path: '/logs',
    title: '操作日志',
    icon: 'List',
    badge: null
  },
  {
    path: '/profile',
    title: '个人设置',
    icon: 'Setting',
    badge: null
  }
])

</script>

<style scoped>
/* 简化的动画变量 - 减少复杂动画提升性能 */
@keyframes simpleScale {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

.sidebar {
  width: 260px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  /* 统一动画时间和缓动函数 */
  transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              min-width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  min-width: 260px;
  /* 移除过度的GPU加速，避免渲染问题 */
}

.sidebar.collapsed {
  width: 64px;
  min-width: 64px;
}

/* 确保收缩状态下的内容与顶部汉堡菜单按钮对齐 */
.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  /* 统一过渡时间 */
  transition: padding 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 收缩状态下的导航区域对齐 */
.sidebar.collapsed .sidebar-nav {
  padding: var(--space-2) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.nav-section {
  margin-bottom: var(--space-6);
  overflow: hidden;
}

.section-title {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
  /* 统一过渡时间为0.2s */
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}


.sidebar.collapsed .nav-section {
  margin-bottom: 0;
  display: contents;
}

.sidebar.collapsed .nav-items {
  display: contents;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: 0 var(--space-2);
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  /* 统一过渡时间和属性 */
  transition: background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  min-height: 44px;
  overflow: hidden;
  white-space: nowrap;
  /* 简化GPU加速，只在必要时使用 */
  will-change: background-color;
}

/* 优化按压效果 */
.nav-item:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 导航时的动画控制 - 更精确的控制 */
.nav-item.no-transition {
  transition: none !important;
}

.nav-item.no-transition * {
  transition: none !important;
  animation: none !important;
}

/* 只在路由切换且非展开/收起时禁用动画 */
.sidebar.is-navigating:not(.sidebar-toggling) .nav-item {
  transition: background-color 0.1s ease, color 0.1s ease !important;
}

.sidebar.is-navigating:not(.sidebar-toggling) .nav-icon {
  transition: transform 0.1s ease !important;
}

/* 收缩状态下的普通菜单项对齐 */
.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0;
  margin: 0 0 6px 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  /* 统一过渡时间 */
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 收缩状态下的按压效果 */
.sidebar.collapsed .nav-item:active {
  transform: scale(0.9);
}

/* 收缩状态下强制隐藏文字 */
.sidebar.collapsed .nav-text {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  /* 移除 translateX 动画，避免残影 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 简化悬停效果，提升流畅性 */
.nav-item:hover .nav-icon {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

/* 收缩状态下的悬停效果 */
.sidebar.collapsed .nav-item:hover {
  transform: translate3d(0, -2px, 0) scale(1.05);
  background: rgba(99, 102, 241, 0.1);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
  border-radius: var(--radius-lg) !important;
}

.sidebar.collapsed .nav-item:hover .nav-icon {
  /* 移除旋转动画，使用简单的缩放 */
  transform: scale(1.2);
  transition: transform 0.15s ease;
}

/* 展开状态下的active样式 */
.sidebar:not(.collapsed) .nav-item.active {
  background: rgb(99 102 241 / 0.1) !important;
  color: var(--primary) !important;
  font-weight: 500 !important;
  transition: background-color 0.15s ease,
              color 0.15s ease,
              box-shadow 0.15s ease !important;
  box-shadow: inset 0 1px 3px rgba(99, 102, 241, 0.1) !important;
}

.sidebar:not(.collapsed) .nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 0 2px 2px 0;
  /* 移除动画，直接显示，避免残影 */
  opacity: 1;
  transition: opacity 0.15s ease;
}

/* 非激活状态时隐藏伪元素 */
.sidebar:not(.collapsed) .nav-item:not(.active)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.15s ease;
}

/* 收缩状态下的active样式 */
.sidebar.collapsed .nav-item.active {
  background: var(--primary) !important;
  color: white !important;
  box-shadow: 0 2px 12px rgba(99, 102, 241, 0.3) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--radius-lg) !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 0 6px 0 !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  transition: background-color 0.15s ease,
              color 0.15s ease,
              box-shadow 0.15s ease,
              transform 0.15s ease !important;
  /* 移除 pulse 动画，避免性能问题 */
  /* animation: pulse 2s infinite; */
}

.sidebar.collapsed .nav-item.active::before {
  display: none;
}

.sidebar.collapsed .nav-item.active:hover {
  background: var(--primary-hover) !important;
  transform: translate3d(0, -2px, 0) scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5) !important;
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: transform 0.15s ease;
  /* 防止图标动画残影 */
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 收缩状态下的图标优化 */
.sidebar.collapsed .nav-icon {
  width: 20px;
  height: 20px;
  font-size: var(--text-lg);
  opacity: 1 !important;
  visibility: visible !important;
}

.nav-text {
  flex: 1;
  font-size: var(--text-sm);
  font-weight: 500;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  min-width: 0;
}

.nav-badge {
  background: var(--danger);
  color: white;
  font-size: var(--text-xs);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 72px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 展开状态的按钮 */
.collapse-btn-expanded {
  position: absolute;
  width: calc(100% - var(--space-4) * 2);
  height: 40px;
  background: var(--bg-hover);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 1;
  transform: scale(1);
}

.collapse-btn-expanded:active {
  transform: scale(0.95);
}

/* 收缩状态的按钮 */
.collapse-btn-collapsed {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  opacity: 0;
  transform: scale(0.8) rotate(-180deg);
}

.collapse-btn-collapsed:active {
  transform: scale(0.9) rotate(-180deg);
}


/* 收缩状态 */
.sidebar.collapsed .sidebar-footer {
  padding: var(--space-2);
  border-top: none;
  height: 52px;
}

.sidebar.collapsed .collapse-btn-expanded {
  opacity: 0;
  transform: scale(0.8);
}

.sidebar.collapsed .collapse-btn-collapsed {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

/* 悬停效果 */
.collapse-btn-expanded:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--primary);
}

.collapse-btn-collapsed:hover {
  background: var(--primary-hover);
  transform: scale(1.05);
}

/* 统一fade过渡动画时间 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}


/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
  
  /* 移动端优化 */
  .nav-item {
    min-height: 48px;
  }
}

/* 全局优化：减少重绘和重排 */
.sidebar * {
  /* 避免不必要的重绘 */
  contain: layout style paint;
}

/* 导航项容器优化 */
.nav-items {
  /* 创建新的层叠上下文，隔离动画影响 */
  isolation: isolate;
}

/* 清理动画残留 */
.nav-item::before,
.nav-item::after {
  /* 确保伪元素在非激活状态下完全透明 */
  pointer-events: none;
}

/* 修复快速切换时的闪烁 */
.nav-item {
  /* 防止内容溢出导致的闪烁 */
  overflow: hidden;
  /* 确保文字不会导致布局偏移 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 优化导航时的动画处理 - 避免过度禁用 */
.sidebar.is-navigating:not(.sidebar-toggling) .fade-enter-active,
.sidebar.is-navigating:not(.sidebar-toggling) .fade-leave-active {
  /* 加快fade动画，减少视觉延迟 */
  transition-duration: 0.1s !important;
}

/* 侧边栏切换时的优化 */
.sidebar.sidebar-toggling {
  /* 确保切换动画优先级最高 */
  z-index: 1001;
}

.sidebar.sidebar-toggling .fade-enter-active,
.sidebar.sidebar-toggling .fade-leave-active {
  /* 切换时保持正常的fade动画时间 */
  transition: opacity 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 优化收缩/展开动画 */
.sidebar,
.sidebar * {
  /* 防止动画期间的闪烁 */
  -webkit-tap-highlight-color: transparent;
}

/* 确保激活状态切换时的平滑过渡 */
.nav-item {
  /* 使用 transform 而不是 position 来避免重排 */
  position: relative;
  z-index: 1;
}

.nav-item.active {
  /* 提升激活项的层级，避免与其他元素冲突 */
  z-index: 2;
}

/* 修复伪元素可能的残留 */
.nav-item::before {
  /* 确保伪元素始终在正确的位置 */
  pointer-events: none;
  will-change: opacity;
}

/* 优化移动端性能 */
@media (hover: none) and (pointer: coarse) {
  .nav-item {
    /* 移动端禁用 hover 动画 */
    transition: background-color 0.15s ease, color 0.15s ease;
  }
  
  .nav-item:hover {
    transform: none;
  }
}
</style>
